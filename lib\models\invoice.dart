class Invoice {
  final String id;
  final String clientName;
  final String clientNumber;
  final String products;
  final List<InvoiceItem> items;
  final String deliveryLocation;
  final String? deliveryDetails; // Nouveau champ pour les détails de livraison
  final double deliveryPrice;
  final double discountAmount; // Nouveau champ pour la remise
  final double advance;
  final double subtotal;
  final double total;
  final String? notes;
  final String? logoPath;
  final String? footerNote;
  final InvoiceStatus status;
  final DateTime createdAt;

  // Champs spécifiques aux factures proforma
  final InvoiceType type;
  final DateTime? validityDate; // Date de validité pour proforma
  final String? clientAddress; // Adresse du client
  final String? clientEmail; // Email du client
  final String? companyRccm; // N° RCCM de l'entreprise
  final String? companyTaxNumber; // N° de contribuable
  final List<String>? paymentMethods; // Modes de paiement acceptés
  final String? specialConditions; // Conditions spécifiques

  Invoice({
    required this.id,
    required this.clientName,
    required this.clientNumber,
    required this.products,
    required this.items,
    required this.deliveryLocation,
    this.deliveryDetails,
    required this.deliveryPrice,
    this.discountAmount = 0.0, // Valeur par défaut pour la remise
    required this.advance,
    required this.subtotal,
    required this.total,
    this.notes,
    this.logoPath,
    this.footerNote,
    required this.status,
    required this.createdAt,
    this.type = InvoiceType.normale, // Type par défaut
    this.validityDate,
    this.clientAddress,
    this.clientEmail,
    this.companyRccm,
    this.companyTaxNumber,
    this.paymentMethods,
    this.specialConditions,
  });

  // Getter pour le numéro de facture formaté
  String get invoiceNumber {
    final prefix = type == InvoiceType.proforma ? 'PRO' : 'HCP';
    return '$prefix-${id.substring(0, 8).toUpperCase()}';
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'clientName': clientName,
      'clientNumber': clientNumber,
      'products': products,
      'items': items.map((item) => item.toJson()).toList(),
      'deliveryLocation': deliveryLocation,
      'deliveryDetails': deliveryDetails,
      'deliveryPrice': deliveryPrice,
      'discountAmount': discountAmount,
      'advance': advance,
      'subtotal': subtotal,
      'total': total,
      'notes': notes,
      'logoPath': logoPath,
      'footerNote': footerNote,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'type': type.name,
      'validityDate': validityDate?.toIso8601String(),
      'clientAddress': clientAddress,
      'clientEmail': clientEmail,
      'companyRccm': companyRccm,
      'companyTaxNumber': companyTaxNumber,
      'paymentMethods': paymentMethods,
      'specialConditions': specialConditions,
    };
  }

  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
      id: json['id'],
      clientName: json['clientName'],
      clientNumber: json['clientNumber'],
      products: json['products'],
      items:
          (json['items'] as List)
              .map((item) => InvoiceItem.fromJson(item))
              .toList(),
      deliveryLocation: json['deliveryLocation'] as String,
      deliveryDetails: json['deliveryDetails'] as String?,
      deliveryPrice: (json['deliveryPrice'] as num).toDouble(),
      discountAmount: (json['discountAmount'] as num?)?.toDouble() ?? 0.0,
      advance: (json['advance'] as num).toDouble(),
      subtotal: json['subtotal'].toDouble(),
      total: json['total'].toDouble(),
      notes: json['notes'],
      logoPath: json['logoPath'],
      footerNote: json['footerNote'],
      status: InvoiceStatus.values.firstWhere(
        (status) => status.name == json['status'],
      ),
      createdAt: DateTime.parse(json['createdAt']),
      type:
          json['type'] != null
              ? InvoiceType.values.firstWhere(
                (type) => type.name == json['type'],
                orElse: () => InvoiceType.normale,
              )
              : InvoiceType.normale,
      validityDate:
          json['validityDate'] != null
              ? DateTime.parse(json['validityDate'])
              : null,
      clientAddress: json['clientAddress'],
      clientEmail: json['clientEmail'],
      companyRccm: json['companyRccm'],
      companyTaxNumber: json['companyTaxNumber'],
      paymentMethods:
          json['paymentMethods'] != null
              ? List<String>.from(json['paymentMethods'])
              : null,
      specialConditions: json['specialConditions'],
    );
  }

  Invoice copyWith({
    String? id,
    String? clientName,
    String? clientNumber,
    String? products,
    List<InvoiceItem>? items,
    String? deliveryLocation,
    String? deliveryDetails,
    double? deliveryPrice,
    double? discountAmount,
    double? advance,
    double? subtotal,
    double? total,
    String? notes,
    String? logoPath,
    String? footerNote,
    InvoiceStatus? status,
    DateTime? createdAt,
    InvoiceType? type,
    DateTime? validityDate,
    String? clientAddress,
    String? clientEmail,
    String? companyRccm,
    String? companyTaxNumber,
    List<String>? paymentMethods,
    String? specialConditions,
  }) {
    return Invoice(
      id: id ?? this.id,
      clientName: clientName ?? this.clientName,
      clientNumber: clientNumber ?? this.clientNumber,
      products: products ?? this.products,
      items: items ?? this.items,
      deliveryLocation: deliveryLocation ?? this.deliveryLocation,
      deliveryDetails: deliveryDetails ?? this.deliveryDetails,
      deliveryPrice: deliveryPrice ?? this.deliveryPrice,
      discountAmount: discountAmount ?? this.discountAmount,
      advance: advance ?? this.advance,
      subtotal: subtotal ?? this.subtotal,
      total: total ?? this.total,
      notes: notes ?? this.notes,
      logoPath: logoPath ?? this.logoPath,
      footerNote: footerNote ?? this.footerNote,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      type: type ?? this.type,
      validityDate: validityDate ?? this.validityDate,
      clientAddress: clientAddress ?? this.clientAddress,
      clientEmail: clientEmail ?? this.clientEmail,
      companyRccm: companyRccm ?? this.companyRccm,
      companyTaxNumber: companyTaxNumber ?? this.companyTaxNumber,
      paymentMethods: paymentMethods ?? this.paymentMethods,
      specialConditions: specialConditions ?? this.specialConditions,
    );
  }
}

class InvoiceItem {
  final String id;
  final String name;
  final double price;
  final int quantity;
  final bool isCustom;
  final String? categoryName;
  final String? productId; // ID du produit en stock
  final bool isFromStock; // Indique si l'article provient du stock

  InvoiceItem({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    required this.isCustom,
    this.categoryName,
    this.productId,
    this.isFromStock = false,
  });

  double get total => price * quantity;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'quantity': quantity,
      'isCustom': isCustom,
      'categoryName': categoryName,
      'productId': productId,
      'isFromStock': isFromStock,
    };
  }

  factory InvoiceItem.fromJson(Map<String, dynamic> json) {
    return InvoiceItem(
      id: json['id'],
      name: json['name'],
      price: json['price'].toDouble(),
      quantity: json['quantity'],
      isCustom: json['isCustom'],
      categoryName: json['categoryName'],
      productId: json['productId'],
      isFromStock: json['isFromStock'] ?? false,
    );
  }

  InvoiceItem copyWith({
    String? id,
    String? name,
    double? price,
    int? quantity,
    bool? isCustom,
    String? categoryName,
    String? productId,
    bool? isFromStock,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      isCustom: isCustom ?? this.isCustom,
      categoryName: categoryName ?? this.categoryName,
      productId: productId ?? this.productId,
      isFromStock: isFromStock ?? this.isFromStock,
    );
  }
}

enum InvoiceStatus {
  enAttente('En attente'),
  payee('Payée'),
  annulee('Annulée');

  const InvoiceStatus(this.displayName);
  final String displayName;
}

enum InvoiceType {
  normale('Facture Normale'),
  proforma('Facture Proforma');

  const InvoiceType(this.displayName);
  final String displayName;
}

// Catégories prédéfinies HCP-DESIGN
class PredefinedCategories {
  static const List<Map<String, dynamic>> categories = [
    {'name': 'Design Graphique', 'price': 15000},
    {'name': 'Logo', 'price': 25000},
    {'name': 'Site Web', 'price': 50000},
    {'name': 'Impression', 'price': 5000},
    {'name': 'Marketing Digital', 'price': 20000},
    {'name': 'Photographie', 'price': 30000},
  ];
}

// Zones de livraison HCP-DESIGN
class DeliveryZones {
  static const Map<String, double> zones = {
    'Bingerville': 1000,
    'Cocody': 1000,
    'Adjamé': 1500,
    'Abobo': 1500,
    'Yopougon': 1500,
    'Treichville': 1500,
    'Marcory': 1500,
    'Koumassi': 1500,
    'Grand-Bassam': 2500,
    'Ebimpé': 2500,
    'Anyama': 2500,
    'Bonoua': 2500,
    'Expedition': 2500,
    'Récupération': 0,
    'Yango': 0,
  };

  static double getDeliveryPrice(String zone) {
    return zones[zone] ?? 0;
  }

  static List<String> get availableZones => zones.keys.toList();
}
