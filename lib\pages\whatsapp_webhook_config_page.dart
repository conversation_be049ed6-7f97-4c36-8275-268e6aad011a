import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import '../services/whatsapp_webhook_service.dart';

class WhatsAppWebhookConfigPage extends StatefulWidget {
  const WhatsAppWebhookConfigPage({super.key});

  @override
  State<WhatsAppWebhookConfigPage> createState() =>
      _WhatsAppWebhookConfigPageState();
}

class _WhatsAppWebhookConfigPageState extends State<WhatsAppWebhookConfigPage>
    with TickerProviderStateMixin {
  final WhatsAppWebhookService _webhookService = WhatsAppWebhookService();

  final TextEditingController _accessTokenController = TextEditingController();
  final TextEditingController _phoneNumberIdController =
      TextEditingController();
  final TextEditingController _verifyTokenController = TextEditingController();
  final TextEditingController _webhookUrlController = TextEditingController();

  bool _isLoading = true;
  bool _isSaving = false;
  bool _isServerRunning = false;
  bool _obscureAccessToken = true;

  // Contrôleurs d'animation
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadConfiguration();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _accessTokenController.dispose();
    _phoneNumberIdController.dispose();
    _verifyTokenController.dispose();
    _webhookUrlController.dispose();
    super.dispose();
  }

  Future<void> _loadConfiguration() async {
    setState(() => _isLoading = true);

    try {
      await _webhookService.initialize();

      setState(() {
        _accessTokenController.text = _webhookService.accessToken;
        _phoneNumberIdController.text = _webhookService.phoneNumberId;
        _verifyTokenController.text = _webhookService.verifyToken;
        _webhookUrlController.text = _webhookService.webhookUrl;
        _isServerRunning = _webhookService.isServerRunning;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveConfiguration() async {
    setState(() => _isSaving = true);

    try {
      await _webhookService.updateConfiguration(
        accessToken: _accessTokenController.text.trim(),
        phoneNumberId: _phoneNumberIdController.text.trim(),
        verifyToken: _verifyTokenController.text.trim(),
        webhookUrl: _webhookUrlController.text.trim(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Configuration sauvegardée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sauvegarde: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isSaving = false);
    }
  }

  Future<void> _toggleWebhookServer() async {
    try {
      if (_isServerRunning) {
        await _webhookService.stopWebhookServer();
        setState(() => _isServerRunning = false);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Serveur webhook arrêté'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } else {
        final success = await _webhookService.startWebhookServer();
        setState(() => _isServerRunning = success);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success
                    ? 'Serveur webhook démarré sur le port 8081'
                    : 'Erreur lors du démarrage du serveur',
              ),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuration WhatsApp'),
        backgroundColor: const Color(0xFF25D366),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelp,
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child:
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        _buildInfoCard(),
                        const SizedBox(height: 16),
                        _buildServerStatusCard(),
                        const SizedBox(height: 16),
                        _buildConfigurationForm(),
                        const SizedBox(height: 24),
                        _buildActionButtons(),
                      ],
                    ),
                  ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[700], size: 24),
                const SizedBox(width: 12),
                const Text(
                  'Configuration WhatsApp Business API',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Configurez votre intégration WhatsApp Business API pour recevoir et envoyer des messages automatiquement. '
              'Vous devez avoir un compte WhatsApp Business API actif.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServerStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isServerRunning ? Icons.cloud_done : Icons.cloud_off,
                  color: _isServerRunning ? Colors.green : Colors.grey,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  _isServerRunning
                      ? 'Serveur Webhook Actif'
                      : 'Serveur Webhook Inactif',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _isServerRunning
                  ? 'Le serveur webhook écoute sur le port 8081 et peut recevoir les messages WhatsApp.'
                  : 'Le serveur webhook est arrêté. Démarrez-le pour recevoir les messages.',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: _toggleWebhookServer,
              icon: Icon(_isServerRunning ? Icons.stop : Icons.play_arrow),
              label: Text(
                _isServerRunning ? 'Arrêter le Serveur' : 'Démarrer le Serveur',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    _isServerRunning ? Colors.red[700] : Colors.green[700],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Paramètres API',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _accessTokenController,
              decoration: InputDecoration(
                labelText: 'Access Token *',
                border: const OutlineInputBorder(),
                hintText: 'EAAxxxxxxxxxxxxxxx',
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureAccessToken
                        ? Icons.visibility
                        : Icons.visibility_off,
                  ),
                  onPressed:
                      () => setState(
                        () => _obscureAccessToken = !_obscureAccessToken,
                      ),
                ),
              ),
              obscureText: _obscureAccessToken,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _phoneNumberIdController,
              decoration: const InputDecoration(
                labelText: 'Phone Number ID *',
                border: OutlineInputBorder(),
                hintText: '1234567890123456',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _verifyTokenController,
              decoration: const InputDecoration(
                labelText: 'Verify Token *',
                border: OutlineInputBorder(),
                hintText: 'mon_token_secret',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _webhookUrlController,
              decoration: const InputDecoration(
                labelText: 'Webhook URL',
                border: OutlineInputBorder(),
                hintText: 'https://votre-domaine.com/webhook',
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '* Champs obligatoires',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        ElevatedButton.icon(
          onPressed: _isSaving ? null : _saveConfiguration,
          icon:
              _isSaving
                  ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Icon(Icons.save),
          label: Text(_isSaving ? 'Sauvegarde...' : 'Sauvegarder'),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF25D366),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            minimumSize: const Size(double.infinity, 48),
          ),
        ),
        const SizedBox(height: 12),
        OutlinedButton.icon(
          onPressed: _testWebhook,
          icon: const Icon(Icons.play_arrow),
          label: const Text('Tester la Configuration'),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            minimumSize: const Size(double.infinity, 48),
          ),
        ),
      ],
    );
  }

  Future<void> _testWebhook() async {
    if (_accessTokenController.text.trim().isEmpty ||
        _phoneNumberIdController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Veuillez remplir au moins l\'Access Token et le Phone Number ID',
          ),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Afficher un dialogue de test
    showDialog(
      context: context,
      builder:
          (context) => _WebhookTestDialog(
            accessToken: _accessTokenController.text.trim(),
            phoneNumberId: _phoneNumberIdController.text.trim(),
            verifyToken: _verifyTokenController.text.trim(),
            webhookService: _webhookService,
          ),
    );
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Aide Configuration WhatsApp'),
            content: const SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Comment obtenir les paramètres:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('1. Créez un compte Meta for Developers'),
                  Text('2. Créez une application WhatsApp Business'),
                  Text('3. Obtenez votre Access Token depuis la console'),
                  Text('4. Notez votre Phone Number ID'),
                  Text('5. Créez un Verify Token unique'),
                  SizedBox(height: 16),
                  Text(
                    'Configuration du Webhook:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• URL: https://votre-domaine.com/webhook'),
                  Text('• Verify Token: celui que vous avez créé'),
                  Text('• Événements: messages, message_deliveries'),
                  SizedBox(height: 16),
                  Text(
                    'Liens utiles:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• Meta for Developers: developers.facebook.com'),
                  Text(
                    '• Documentation: developers.facebook.com/docs/whatsapp',
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
              TextButton(
                onPressed: () {
                  Clipboard.setData(
                    const ClipboardData(
                      text: 'https://developers.facebook.com/docs/whatsapp',
                    ),
                  );
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Lien copié dans le presse-papiers'),
                      backgroundColor: Colors.blue,
                    ),
                  );
                },
                child: const Text('Copier le Lien'),
              ),
            ],
          ),
    );
  }
}

class _WebhookTestDialog extends StatefulWidget {
  final String accessToken;
  final String phoneNumberId;
  final String verifyToken;
  final WhatsAppWebhookService webhookService;

  const _WebhookTestDialog({
    required this.accessToken,
    required this.phoneNumberId,
    required this.verifyToken,
    required this.webhookService,
  });

  @override
  State<_WebhookTestDialog> createState() => _WebhookTestDialogState();
}

class _WebhookTestDialogState extends State<_WebhookTestDialog> {
  final TextEditingController _testPhoneController = TextEditingController();
  final TextEditingController _testMessageController = TextEditingController();

  bool _isTesting = false;
  final List<String> _testResults = [];

  @override
  void dispose() {
    _testPhoneController.dispose();
    _testMessageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          children: [
            AppBar(
              title: const Text('Test WhatsApp API'),
              backgroundColor: const Color(0xFF25D366),
              foregroundColor: Colors.white,
              automaticallyImplyLeading: false,
              actions: [
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const Text(
                      'Test de Configuration',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Test 1: Vérification des informations
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '1. Vérification des Paramètres',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Access Token: ${_maskToken(widget.accessToken)}',
                            ),
                            Text('Phone Number ID: ${widget.phoneNumberId}'),
                            Text(
                              'Verify Token: ${widget.verifyToken.isNotEmpty ? "✓ Configuré" : "✗ Non configuré"}',
                            ),
                            const SizedBox(height: 8),
                            ElevatedButton(
                              onPressed: _isTesting ? null : _testConfiguration,
                              child: const Text('Tester la Configuration'),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Test 2: Envoi de message
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '2. Test d\'Envoi de Message',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            TextField(
                              controller: _testPhoneController,
                              decoration: const InputDecoration(
                                labelText: 'Numéro de téléphone',
                                hintText: '+33123456789',
                                border: OutlineInputBorder(),
                              ),
                            ),
                            const SizedBox(height: 8),
                            TextField(
                              controller: _testMessageController,
                              decoration: const InputDecoration(
                                labelText: 'Message de test',
                                hintText: 'Bonjour, ceci est un test',
                                border: OutlineInputBorder(),
                              ),
                              maxLines: 2,
                            ),
                            const SizedBox(height: 8),
                            ElevatedButton(
                              onPressed: _isTesting ? null : _testSendMessage,
                              child: const Text('Envoyer Message Test'),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Test 3: Serveur webhook
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '3. Test du Serveur Webhook',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Statut: ${widget.webhookService.isServerRunning ? "✓ Actif" : "✗ Inactif"}',
                            ),
                            const SizedBox(height: 8),
                            ElevatedButton(
                              onPressed: _isTesting ? null : _testWebhookServer,
                              child: const Text('Tester le Serveur'),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Résultats des tests
                    if (_testResults.isNotEmpty) ...[
                      const Text(
                        'Résultats des Tests:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children:
                              _testResults.map((result) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 4),
                                  child: Text(
                                    result,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color:
                                          result.startsWith('✓')
                                              ? Colors.green[700]
                                              : result.startsWith('✗')
                                              ? Colors.red[700]
                                              : Colors.grey[700],
                                    ),
                                  ),
                                );
                              }).toList(),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _maskToken(String token) {
    if (token.length <= 8) return token;
    return '${token.substring(0, 4)}...${token.substring(token.length - 4)}';
  }

  Future<void> _testConfiguration() async {
    setState(() {
      _isTesting = true;
      _testResults.clear();
    });

    try {
      // Test 1: Vérifier que les paramètres sont valides
      if (widget.accessToken.isEmpty) {
        _addTestResult('✗ Access Token manquant');
      } else if (widget.accessToken.length < 20) {
        _addTestResult('✗ Access Token semble invalide (trop court)');
      } else {
        _addTestResult('✓ Access Token présent');
      }

      if (widget.phoneNumberId.isEmpty) {
        _addTestResult('✗ Phone Number ID manquant');
      } else if (!RegExp(r'^\d+$').hasMatch(widget.phoneNumberId)) {
        _addTestResult(
          '✗ Phone Number ID doit contenir uniquement des chiffres',
        );
      } else {
        _addTestResult('✓ Phone Number ID valide');
      }

      if (widget.verifyToken.isEmpty) {
        _addTestResult(
          '⚠ Verify Token non configuré (optionnel pour les tests)',
        );
      } else {
        _addTestResult('✓ Verify Token configuré');
      }

      // Test 2: Tenter une requête à l'API WhatsApp
      await _testApiConnection();
    } catch (e) {
      _addTestResult('✗ Erreur lors du test: $e');
    } finally {
      setState(() => _isTesting = false);
    }
  }

  Future<void> _testApiConnection() async {
    try {
      // Tester l'accès à l'API en récupérant les informations du numéro de téléphone
      final response = await http.get(
        Uri.parse('https://graph.facebook.com/v18.0/${widget.phoneNumberId}'),
        headers: {'Authorization': 'Bearer ${widget.accessToken}'},
      );

      if (response.statusCode == 200) {
        _addTestResult('✓ Connexion à l\'API WhatsApp réussie');
        final data = jsonDecode(response.body);
        if (data['display_phone_number'] != null) {
          _addTestResult(
            '✓ Numéro de téléphone: ${data['display_phone_number']}',
          );
        }
      } else {
        _addTestResult(
          '✗ Erreur API: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      _addTestResult('✗ Erreur de connexion à l\'API: $e');
    }
  }

  Future<void> _testSendMessage() async {
    final phone = _testPhoneController.text.trim();
    final message = _testMessageController.text.trim();

    if (phone.isEmpty || message.isEmpty) {
      _addTestResult('✗ Veuillez remplir le numéro et le message');
      return;
    }

    setState(() => _isTesting = true);

    try {
      final response = await http.post(
        Uri.parse(
          'https://graph.facebook.com/v18.0/${widget.phoneNumberId}/messages',
        ),
        headers: {
          'Authorization': 'Bearer ${widget.accessToken}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'messaging_product': 'whatsapp',
          'to': phone,
          'type': 'text',
          'text': {'body': message},
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _addTestResult('✓ Message envoyé avec succès');
        _addTestResult('  ID: ${data['messages'][0]['id']}');
      } else {
        _addTestResult(
          '✗ Erreur envoi: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      _addTestResult('✗ Erreur lors de l\'envoi: $e');
    } finally {
      setState(() => _isTesting = false);
    }
  }

  Future<void> _testWebhookServer() async {
    setState(() => _isTesting = true);

    try {
      if (!widget.webhookService.isServerRunning) {
        _addTestResult('✗ Le serveur webhook n\'est pas démarré');
        _addTestResult('  Démarrez-le depuis la page de configuration');
      } else {
        _addTestResult('✓ Serveur webhook actif sur le port 8081');

        // Test de connexion locale
        try {
          final response = await http
              .get(
                Uri.parse(
                  'http://localhost:8081/webhook?hub.mode=subscribe&hub.verify_token=${widget.verifyToken}&hub.challenge=test123',
                ),
              )
              .timeout(const Duration(seconds: 5));

          if (response.statusCode == 200 && response.body == 'test123') {
            _addTestResult(
              '✓ Webhook répond correctement aux requêtes de vérification',
            );
          } else {
            _addTestResult(
              '✗ Webhook ne répond pas correctement (${response.statusCode})',
            );
          }
        } catch (e) {
          _addTestResult('✗ Impossible de se connecter au webhook local: $e');
        }
      }
    } finally {
      setState(() => _isTesting = false);
    }
  }

  void _addTestResult(String result) {
    setState(() {
      _testResults.add(
        '${DateTime.now().toString().substring(11, 19)} - $result',
      );
    });
  }
}
