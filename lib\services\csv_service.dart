import 'package:csv/csv.dart';
import 'package:generateur_facture/models/product.dart';
import 'package:generateur_facture/models/category.dart'; // Utilisé pour obtenir le nom de la catégorie
import 'package:flutter/foundation.dart'
    hide
        Category; // Pour debugPrint, en cachant Category pour éviter le conflit
import 'package:generateur_facture/services/inventory_service.dart'; // Pour récupérer les catégories

class CsvService {
  final InventoryService _inventoryService = InventoryService.instance;

  Future<String> exportProductsToCsv(List<Product> products) async {
    List<Category> categories = await _inventoryService.getCategories();
    Map<String, String> categoryMap = {
      for (var cat in categories) cat.id: cat.name,
    };

    List<List<dynamic>> rows = [];
    // En-têtes CSV
    rows.add([
      'ID',
      'Nom',
      'Prix',
      'Quantité',
      'Description',
      'ID Catégorie',
      'Nom Catégorie',
      'URL Image',
    ]);

    for (var product in products) {
      rows.add([
        product.id,
        product.name,
        product.price,
        product.quantity,
        product.description,
        product.categoryId,
        categoryMap[product.categoryId] ?? 'N/A', // Nom de la catégorie
        product.imageUrl ?? '',
      ]);
    }
    return const ListToCsvConverter().convert(rows);
  }

  // Note: L'importation est plus complexe car elle nécessite de gérer les catégories (création si inexistante ou liaison)
  // et potentiellement la validation des données.
  // Pour cet exemple, nous allons supposer que les catégories existent déjà par leur nom.
  Future<List<Product>> importProductsFromCsv(String csvString) async {
    List<List<dynamic>> csvTable = const CsvToListConverter(
      eol: '\n',
      fieldDelimiter: ',',
    ).convert(csvString);
    List<Product> importedProducts = [];
    List<Category> existingCategories = await _inventoryService.getCategories();

    if (csvTable.length < 2) {
      // Au moins une ligne d'en-tête et une ligne de données
      throw Exception('Fichier CSV vide ou ne contient que les en-têtes.');
    }

    // Valider les en-têtes (optionnel mais recommandé)
    List<dynamic> headerRow = csvTable.first;
    // Exemple de validation d'en-tête (à adapter selon le format attendu)
    if (headerRow.length < 7 ||
        headerRow[0].toString().toLowerCase().trim() != 'nom' ||
        headerRow[1].toString().toLowerCase().trim() != 'prix' ||
        headerRow[2].toString().toLowerCase().trim() != 'quantité' ||
        headerRow[3].toString().toLowerCase().trim() != 'description' ||
        headerRow[4].toString().toLowerCase().trim() != 'nom catégorie' ||
        headerRow[5].toString().toLowerCase().trim() !=
            'id catégorie' || // Ou 'nom catégorie' si on importe par nom
        headerRow[6].toString().toLowerCase().trim() != 'url image') {
      // Ajustement: l'ordre des colonnes dans le CSV d'import peut être différent de l'export
      // L'utilisateur doit fournir un CSV avec: Nom, Prix, Quantité, Description, Nom Catégorie, URL Image
      // L'ID Produit et ID Catégorie seront gérés par le service.
      // Pour cet exemple, on attend: Nom, Prix, Quantité, Description, Nom Catégorie, URL Image
      // On va simplifier pour l'import: Nom, Prix, Quantité, Description, Nom Catégorie, URL Image
      throw Exception(
        'Format d\'en-tête CSV invalide. Attendu: Nom, Prix, Quantité, Description, Nom Catégorie, URL Image',
      );
    }

    for (int i = 1; i < csvTable.length; i++) {
      List<dynamic> row = csvTable[i];
      if (row.length < 5) {
        // S'assurer qu'on a au moins les champs obligatoires
        debugPrint('Ligne ignorée (pas assez de colonnes): $row');
        continue;
      }

      String productName = row[0].toString().trim();
      double? productPrice = double.tryParse(row[1].toString().trim());
      int? productQuantity = int.tryParse(row[2].toString().trim());
      String productDescription = row[3].toString().trim();
      String categoryName = row[4].toString().trim();
      String? imageUrl = row.length > 5 ? row[5].toString().trim() : null;

      if (productName.isEmpty ||
          productPrice == null ||
          productQuantity == null ||
          categoryName.isEmpty) {
        debugPrint('Ligne ignorée (données invalides ou manquantes): $row');
        continue;
      }

      // Trouver ou créer la catégorie
      Category? category;
      try {
        category = existingCategories.firstWhere(
          (cat) => cat.name.toLowerCase() == categoryName.toLowerCase(),
        );
      } catch (e) {
        // Catégorie non trouvée
        category = null;
      }
      if (category == null) {
        // Option: créer la catégorie si elle n'existe pas
        // Pour cet exemple, on va ignorer le produit si la catégorie n'existe pas pour simplifier
        // ou lever une exception.
        // category = await _inventoryService.addCategory(Category(id: '', name: categoryName));
        // existingCategories.add(category); // Ajouter à la liste pour les prochaines itérations
        debugPrint(
          'Catégorie "$categoryName" non trouvée pour le produit "$productName". Produit ignoré.',
        );
        continue;
      }

      importedProducts.add(
        Product(
          id: '', // Sera généré par le service lors de l'ajout
          name: productName,
          price: productPrice,
          quantity: productQuantity,
          description: productDescription,
          categoryId: category.id,
          imageUrl: imageUrl?.isNotEmpty == true ? imageUrl : null,
        ),
      );
    }
    return importedProducts;
  }

  String getCsvTemplate() {
    List<List<dynamic>> rows = [];
    // En-têtes CSV
    rows.add([
      'Nom',
      'Prix',
      'Quantité',
      'Description',
      'Nom Catégorie',
      'URL Image',
    ]);

    // Exemple de produit
    rows.add([
      'Produit Exemple',
      '10.00',
      '5',
      'Description du produit',
      'Catégorie Exemple',
      '',
    ]);

    return const ListToCsvConverter().convert(rows);
  }
}
