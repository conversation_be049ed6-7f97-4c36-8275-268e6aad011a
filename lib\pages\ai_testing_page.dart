import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/ai_models.dart';
import '../models/whatsapp_chat.dart';
import '../services/ai_service.dart';
import '../services/knowledge_service.dart';
import '../services/whatsapp_service.dart';

class AITestingPage extends StatefulWidget {
  const AITestingPage({super.key});

  @override
  State<AITestingPage> createState() => _AITestingPageState();
}

class _AITestingPageState extends State<AITestingPage>
    with TickerProviderStateMixin {
  final AIService _aiService = AIService();
  final KnowledgeService _knowledgeService = KnowledgeService();
  final WhatsAppService _whatsappService = WhatsAppService();

  final TextEditingController _testMessageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  final List<TestResult> _testResults = [];
  Map<String, AIPerformanceMetrics> _performanceMetrics = {};
  bool _isLoading = true;
  bool _isTesting = false;
  CustomerIntention _selectedIntention = CustomerIntention.unknown;

  // Contrôleurs d'animation
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadData();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _testMessageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      await _aiService.initialize();
      await _knowledgeService.initialize();
      await _whatsappService.initialize();

      setState(() {
        _performanceMetrics = _aiService.performanceMetrics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _runTest() async {
    final message = _testMessageController.text.trim();
    if (message.isEmpty || _isTesting) return;

    setState(() => _isTesting = true);

    try {
      final startTime = DateTime.now();

      // Test de l'IA
      final aiResponse = await _aiService.generateResponse(
        message: message,
        context: 'Test de l\'IA - Environnement de développement',
        intention: _selectedIntention,
      );

      final endTime = DateTime.now();
      final responseTime = endTime.difference(startTime);

      // Test de la base de connaissances
      final knowledgeAnswer = _knowledgeService.findBestAnswer(message);
      final relevantArticles = _knowledgeService.searchArticles(
        message,
        limit: 3,
      );

      // Créer le résultat du test
      final testResult = TestResult(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        inputMessage: message,
        selectedIntention: _selectedIntention,
        aiResponse: aiResponse,
        knowledgeAnswer: knowledgeAnswer,
        relevantArticles: relevantArticles,
        responseTime: responseTime,
        timestamp: DateTime.now(),
      );

      setState(() {
        _testResults.insert(0, testResult);
        _isTesting = false;
      });

      // Faire défiler vers le haut pour voir le nouveau résultat
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    } catch (e) {
      setState(() => _isTesting = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du test: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _runBatchTests() async {
    final testMessages = [
      'Bonjour, je cherche un iPhone 15',
      'Quel est le prix du Samsung Galaxy S24?',
      'J\'ai un problème avec mon téléphone',
      'Comment suivre ma commande?',
      'Quels sont vos modes de paiement?',
      'Mon écran est cassé, que faire?',
      'Avez-vous des promotions en cours?',
      'Délai de livraison pour Dakar?',
    ];

    for (final message in testMessages) {
      _testMessageController.text = message;
      await _runTest();
      await Future.delayed(const Duration(milliseconds: 500));
    }

    _testMessageController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test & Monitoring IA'),
        backgroundColor: Colors.purple[900],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadData),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showDetailedMetrics,
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child:
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : Column(
                    children: [
                      _buildTestInput(),
                      _buildMetricsOverview(),
                      Expanded(child: _buildTestResults()),
                    ],
                  ),
        ),
      ),
    );
  }

  Widget _buildTestInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.purple[50],
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _testMessageController,
                  decoration: const InputDecoration(
                    hintText: 'Tapez un message de test...',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.chat),
                  ),
                  maxLines: 2,
                  onSubmitted: (_) => _runTest(),
                ),
              ),
              const SizedBox(width: 8),
              Column(
                children: [
                  ElevatedButton(
                    onPressed: _isTesting ? null : _runTest,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple[700],
                      foregroundColor: Colors.white,
                    ),
                    child:
                        _isTesting
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Text('Tester'),
                  ),
                  const SizedBox(height: 4),
                  TextButton(
                    onPressed: _isTesting ? null : _runBatchTests,
                    child: const Text('Tests Auto'),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Text('Intention: '),
              const SizedBox(width: 8),
              Expanded(
                child: DropdownButton<CustomerIntention>(
                  value: _selectedIntention,
                  isExpanded: true,
                  onChanged: (value) {
                    setState(() {
                      _selectedIntention = value!;
                    });
                  },
                  items:
                      CustomerIntention.values.map((intention) {
                        return DropdownMenuItem(
                          value: intention,
                          child: Text(_getIntentionLabel(intention)),
                        );
                      }).toList(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsOverview() {
    if (_performanceMetrics.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[100],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Métriques de Performance',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children:
                  _performanceMetrics.entries.map((entry) {
                    final metrics = entry.value;
                    return Container(
                      margin: const EdgeInsets.only(right: 16),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            entry.key,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 4),
                          Text('Requêtes: ${metrics.totalRequests}'),
                          Text(
                            'Succès: ${(metrics.successRate * 100).toStringAsFixed(1)}%',
                          ),
                          Text(
                            'Temps moy: ${metrics.averageResponseTime.toStringAsFixed(0)}ms',
                          ),
                          Text(
                            'Confiance: ${(metrics.averageConfidence * 100).toStringAsFixed(1)}%',
                          ),
                        ],
                      ),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestResults() {
    if (_testResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.science, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Aucun test effectué',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Commencez par taper un message de test',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _testResults.length,
      itemBuilder: (context, index) {
        final result = _testResults[index];
        return _buildTestResultCard(result);
      },
    );
  }

  Widget _buildTestResultCard(TestResult result) {
    final timeFormat = DateFormat('HH:mm:ss');

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.chat_bubble, color: Colors.purple[700]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    result.inputMessage,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Text(
                  timeFormat.format(result.timestamp),
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getIntentionColor(
                      result.selectedIntention,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getIntentionLabel(result.selectedIntention),
                    style: TextStyle(
                      fontSize: 10,
                      color: _getIntentionColor(result.selectedIntention),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Temps: ${result.responseTime.inMilliseconds}ms',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Réponse IA
            if (result.aiResponse != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.smart_toy,
                          size: 16,
                          color: Colors.blue[700],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Réponse IA (${result.aiResponse!.model.modelName})',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.blue[700],
                          ),
                        ),
                        const Spacer(),
                        Text(
                          'Confiance: ${(result.aiResponse!.confidence * 100).toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue[600],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(result.aiResponse!.content),
                  ],
                ),
              ),
              const SizedBox(height: 8),
            ],

            // Réponse base de connaissances
            if (result.knowledgeAnswer != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.library_books,
                          size: 16,
                          color: Colors.green[700],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Base de Connaissances',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: Colors.green[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(result.knowledgeAnswer!),
                  ],
                ),
              ),
              const SizedBox(height: 8),
            ],

            // Articles pertinents
            if (result.relevantArticles.isNotEmpty) ...[
              Text(
                'Articles pertinents (${result.relevantArticles.length}):',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 4),
              ...result.relevantArticles.map((article) {
                return Padding(
                  padding: const EdgeInsets.only(left: 16, bottom: 2),
                  child: Text(
                    '• ${article.title}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                );
              }),
            ],
          ],
        ),
      ),
    );
  }

  String _getIntentionLabel(CustomerIntention intention) {
    switch (intention) {
      case CustomerIntention.achat:
        return 'Achat';
      case CustomerIntention.information:
        return 'Information';
      case CustomerIntention.support:
        return 'Support';
      case CustomerIntention.unknown:
        return 'Inconnue';
    }
  }

  Color _getIntentionColor(CustomerIntention intention) {
    switch (intention) {
      case CustomerIntention.achat:
        return Colors.green;
      case CustomerIntention.information:
        return Colors.blue;
      case CustomerIntention.support:
        return Colors.orange;
      case CustomerIntention.unknown:
        return Colors.grey;
    }
  }

  void _showDetailedMetrics() {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              constraints: const BoxConstraints(maxWidth: 600, maxHeight: 500),
              child: Column(
                children: [
                  AppBar(
                    title: const Text('Métriques Détaillées'),
                    backgroundColor: Colors.purple[700],
                    foregroundColor: Colors.white,
                    automaticallyImplyLeading: false,
                    actions: [
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children:
                            _performanceMetrics.entries.map((entry) {
                              final metrics = entry.value;
                              return Card(
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        entry.key,
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Requêtes totales: ${metrics.totalRequests}',
                                      ),
                                      Text(
                                        'Requêtes réussies: ${metrics.successfulRequests}',
                                      ),
                                      Text(
                                        'Requêtes échouées: ${metrics.failedRequests}',
                                      ),
                                      Text(
                                        'Taux de succès: ${(metrics.successRate * 100).toStringAsFixed(2)}%',
                                      ),
                                      Text(
                                        'Temps de réponse moyen: ${metrics.averageResponseTime.toStringAsFixed(2)}ms',
                                      ),
                                      Text(
                                        'Confiance moyenne: ${(metrics.averageConfidence * 100).toStringAsFixed(2)}%',
                                      ),
                                      Text(
                                        'Tokens utilisés: ${metrics.totalTokensUsed}',
                                      ),
                                      Text(
                                        'Dernière utilisation: ${DateFormat('dd/MM/yyyy HH:mm').format(metrics.lastUsed)}',
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }
}

class TestResult {
  final String id;
  final String inputMessage;
  final CustomerIntention selectedIntention;
  final AIResponse? aiResponse;
  final String? knowledgeAnswer;
  final List<KnowledgeArticle> relevantArticles;
  final Duration responseTime;
  final DateTime timestamp;

  TestResult({
    required this.id,
    required this.inputMessage,
    required this.selectedIntention,
    this.aiResponse,
    this.knowledgeAnswer,
    required this.relevantArticles,
    required this.responseTime,
    required this.timestamp,
  });
}
