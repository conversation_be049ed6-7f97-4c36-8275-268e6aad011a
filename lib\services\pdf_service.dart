import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:intl/intl.dart';
import '../models/invoice.dart';

class PDFService {
  static final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');
  static final DateFormat _dateFormat = DateFormat('dd/MM/yyyy', 'fr_FR');

  static Future<void> generateAndDownloadInvoicePDF(Invoice invoice) async {
    final pdf = pw.Document();

    // Charger le header en premier car il est async
    final header = await _buildHeader();
    
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            header,
            pw.SizedBox(height: 30),
            _buildInvoiceInfo(invoice),
            pw.SizedBox(height: 20),
            _buildClientInfo(invoice),
            pw.SizedBox(height: 30),
            _buildItemsTable(invoice),
            pw.SizedBox(height: 20),
            _buildTotalSection(invoice, context), // Passer le context ici
            if (invoice.notes != null && invoice.notes!.isNotEmpty)
              pw.SizedBox(height: 20),
            if (invoice.notes != null && invoice.notes!.isNotEmpty)
              _buildNotesSection(invoice.notes!),
            pw.SizedBox(height: 30),
            _buildFooter(invoice.footerNote),
          ];
        },
      ),
    );

    // Sauvegarder et ouvrir le PDF
    await _saveAndOpenPDF(pdf, invoice);
  }

  static Future<pw.Widget> _buildHeader() async {
    // Charger le logo depuis les assets
    pw.ImageProvider? logoImage;
    try {
      final logoData = await rootBundle.load('assets/images/logo_entreprise.png');
      logoImage = pw.MemoryImage(logoData.buffer.asUint8List());
    } catch (e) {
      // Si le logo n'est pas trouvé, on continue sans logo
      logoImage = null;
    }

    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue900,
        borderRadius: pw.BorderRadius.circular(10),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'HCP-DESIGN',
                style: pw.TextStyle(
                  fontSize: 28,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.white,
                ),
              ),
              if (logoImage != null)
                pw.Container(
                  width: 80, // Agrandissement du logo
                  height: 80, // Agrandissement du logo
                  child: pw.Image(
                    logoImage,
                    fit: pw.BoxFit.contain,
                  ),
                ),
            ],
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'Téléphone: +225 07 09 49 58 48',
                    style: const pw.TextStyle(
                      fontSize: 12,
                      color: PdfColors.white,
                    ),
                  ),
                  pw.Text(
                    'Email: <EMAIL>',
                    style: const pw.TextStyle(
                      fontSize: 12,
                      color: PdfColors.white,
                    ),
                  ),
                ],
              ),
              pw.Text(
                'www.hcp-designci.com',
                style: const pw.TextStyle(
                  fontSize: 12,
                  color: PdfColors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildInvoiceInfo(Invoice invoice) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'FACTURE',
              style: pw.TextStyle(
                fontSize: 24,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.blue900,
              ),
            ),
            pw.SizedBox(height: 5),
            pw.Text(
              'N° ${invoice.id.substring(0, 8).toUpperCase()}',
              style: const pw.TextStyle(
                fontSize: 14,
                color: PdfColors.grey700,
              ),
            ),
          ],
        ),
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            pw.Text(
              'Date: ${_dateFormat.format(invoice.createdAt)}',
              style: const pw.TextStyle(fontSize: 12),
            ),
            if (invoice.deliveryDetails != null && invoice.deliveryDetails!.isNotEmpty)
              pw.Padding(
                padding: const pw.EdgeInsets.only(top: 5),
                child: pw.Text(
                  'Détails livraison: ${invoice.deliveryDetails}',
                  style: const pw.TextStyle(
                    fontSize: 10,
                    color: PdfColors.grey700,
                  ),
                ),
              ),
            pw.SizedBox(height: 5),
            pw.Container(
              padding: const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: pw.BoxDecoration(
                color: _getStatusColor(invoice.status),
                borderRadius: pw.BorderRadius.circular(4),
              ),
              child: pw.Text(
                invoice.status.displayName,
                style: const pw.TextStyle(
                  fontSize: 10,
                  color: PdfColors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  static pw.Widget _buildClientInfo(Invoice invoice) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'INFORMATIONS CLIENT',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue900,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Text('Nom: ${invoice.clientName}', style: const pw.TextStyle(fontSize: 12)),
          pw.Text('Téléphone: ${invoice.clientNumber}', style: const pw.TextStyle(fontSize: 12)),
          pw.Text('Description: ${invoice.products}', style: const pw.TextStyle(fontSize: 12)),
          pw.Text('Zone de livraison: ${invoice.deliveryLocation}', style: const pw.TextStyle(fontSize: 12)),
        ],
      ),
    );
  }

  static pw.Widget _buildItemsTable(Invoice invoice) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'DÉTAIL DES ARTICLES',
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue900,
          ),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(3),
            1: const pw.FlexColumnWidth(1),
            2: const pw.FlexColumnWidth(2),
            3: const pw.FlexColumnWidth(2),
          },
          children: [
            // En-tête du tableau
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey100),
              children: [
                _buildTableCell('Article', isHeader: true),
                _buildTableCell('Qté', isHeader: true),
                _buildTableCell('Prix unitaire', isHeader: true),
                _buildTableCell('Total', isHeader: true),
              ],
            ),
            // Lignes des articles
            ...invoice.items.map((item) => pw.TableRow(
              children: [
                _buildTableCell(item.name),
                _buildTableCell(item.quantity.toString()),
                _buildTableCell('${_currencyFormat.format(item.price)} FCFA'),
                _buildTableCell('${_currencyFormat.format(item.total)} FCFA'),
              ],
            )),
          ],
        ),
      ],
    );
  }

  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }

  static pw.Widget _buildTotalSection(Invoice invoice, pw.Context context) {
    return pw.Container(
      width: double.infinity,
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.end,
        children: [
          pw.Container(
            width: 250,
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey300),
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                _buildTotalRow('Sous-total', invoice.subtotal, context),
                _buildTotalRow('Livraison', invoice.deliveryPrice, context),
                if (invoice.discountAmount > 0) // Ajout de l'affichage de la remise
                  _buildTotalRow('Remise', -invoice.discountAmount, context, isDiscount: true),
                if (invoice.advance > 0)
                  _buildTotalRow('Avance', -invoice.advance, context, isAdvance: true),
                pw.Divider(color: PdfColors.grey400, height: 20),
                _buildTotalRow('TOTAL', invoice.total, context, isTotal: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildTotalRow(String label, double value, pw.Context context, {bool isTotal = false, bool isAdvance = false, bool isDiscount = false}) {
    final PdfColor textColor = isAdvance 
        ? PdfColors.orange700 
        : isDiscount 
            ? PdfColors.green700 
            : PdfColors.black;
    final style = isTotal
        ? pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 14, color: PdfColors.blue900)
        : pw.TextStyle(fontSize: 12, color: textColor);
    final valueStyle = isTotal
        ? pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 14, color: PdfColors.blue900)
        : pw.TextStyle(fontSize: 12, color: textColor, fontWeight: pw.FontWeight.bold);

    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(label, style: style),
          pw.Text('${isDiscount ? '' : ''}${_currencyFormat.format(value)} FCFA', style: valueStyle),
        ],
      ),
    );
  }

  static pw.Widget _buildNotesSection(String notes) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'NOTES',
            style: pw.TextStyle(
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue900,
            ),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            notes,
            style: const pw.TextStyle(fontSize: 10),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildFooter(String? customFooterNote) {
    final footerText = customFooterNote?.isNotEmpty == true 
        ? customFooterNote! 
        : 'Merci pour votre confiance - HCP-DESIGN';
    
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(15),
      decoration: const pw.BoxDecoration(
        color: PdfColors.grey100,
      ),
      child: pw.Center(
        child: pw.Text(
          footerText,
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue900,
          ),
        ),
      ),
    );
  }

  static PdfColor _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.payee:
        return PdfColors.green;

      case InvoiceStatus.enAttente:
        return PdfColors.orange;
      case InvoiceStatus.annulee:
        return PdfColors.red;
    }
  }

  static Future<void> _saveAndOpenPDF(pw.Document pdf, Invoice invoice) async {
    try {
      // Générer le nom de fichier
      final fileName = 'Facture_${invoice.id.substring(0, 8)}_${invoice.clientName.replaceAll(' ', '_')}.pdf';
      
      // Sauvegarder le PDF
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
        name: fileName,
      );
    } catch (e) {
      throw Exception('Erreur lors de la génération du PDF: $e');
    }
  }
}