#!/usr/bin/env python3
"""
Script pour créer des icônes PNG de test optimisées pour Flutter
"""

from PIL import Image, ImageDraw
import os

def create_icon(name, size=72):
    """Crée une icône PNG simple"""
    # Créer une image avec fond transparent
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Couleur noire pour l'icône
    color = (0, 0, 0, 255)
    
    # Dessiner selon le type d'icône
    if name == 'dashboard':
        # Grille 3x3 pour dashboard
        for i in range(3):
            for j in range(3):
                x = 10 + i * 18
                y = 10 + j * 18
                draw.rectangle([x, y, x+12, y+12], fill=color)
    
    elif name == 'receipt':
        # Rectangle avec lignes pour facture
        draw.rectangle([15, 5, 57, 67], outline=color, width=3)
        for i in range(4):
            y = 15 + i * 10
            draw.line([20, y, 52, y], fill=color, width=2)
    
    elif name == 'inventory':
        # Boîtes empilées pour inventaire
        draw.rectangle([10, 35, 35, 60], outline=color, width=3)
        draw.rectangle([20, 20, 45, 45], outline=color, width=3)
        draw.rectangle([30, 5, 55, 30], outline=color, width=3)
    
    elif name == 'tasks':
        # Liste avec coches
        draw.rectangle([10, 10, 15, 15], outline=color, width=2)
        draw.line([20, 12, 50, 12], fill=color, width=2)
        draw.rectangle([10, 25, 15, 30], outline=color, width=2)
        draw.line([20, 27, 50, 27], fill=color, width=2)
        draw.rectangle([10, 40, 15, 45], outline=color, width=2)
        draw.line([20, 42, 50, 42], fill=color, width=2)
    
    elif name == 'whatsapp':
        # Bulle de chat
        draw.ellipse([10, 10, 50, 50], outline=color, width=3)
        draw.polygon([(45, 45), (55, 55), (50, 45)], fill=color)
    
    return img

def main():
    """Crée toutes les icônes de test"""
    icons = ['dashboard', 'receipt', 'inventory', 'tasks', 'whatsapp']
    output_dir = 'assets/icons/navigation'
    
    # Créer le dossier s'il n'existe pas
    os.makedirs(output_dir, exist_ok=True)
    
    for icon_name in icons:
        # Créer l'icône
        img = create_icon(icon_name, 72)
        
        # Sauvegarder avec optimisation
        output_path = os.path.join(output_dir, f'{icon_name}_test.png')
        img.save(output_path, 'PNG', optimize=True)
        print(f'Créé: {output_path}')

if __name__ == '__main__':
    main()
