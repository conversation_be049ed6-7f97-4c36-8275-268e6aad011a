import '../models/invoice.dart';
import '../services/invoice_service.dart';
import '../services/inventory_service.dart';

/// Modèle pour représenter un produit hors stock vendu
class OutOfStockProduct {
  final String productId;
  final String productName;
  final double price;
  final int totalQuantitySold;
  final double totalRevenue;
  final List<DateTime> salesDates;
  final String? categoryName;

  OutOfStockProduct({
    required this.productId,
    required this.productName,
    required this.price,
    required this.totalQuantitySold,
    required this.totalRevenue,
    required this.salesDates,
    this.categoryName,
  });
}

/// Service pour gérer les statistiques des produits hors stock vendus
class OutOfStockService {
  static final InventoryService _inventoryService = InventoryService.instance;

  /// Obtenir tous les produits hors stock vendus pour le mois en cours
  static Future<List<OutOfStockProduct>>
  getOutOfStockProductsThisMonth() async {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0, 23, 59, 59);

    return await _getOutOfStockProductsInPeriod(startOfMonth, endOfMonth);
  }

  /// Obtenir tous les produits hors stock vendus dans une période donnée
  static Future<List<OutOfStockProduct>> _getOutOfStockProductsInPeriod(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final invoices = await InvoiceService.loadInvoices(); // Keep using static method as it's still static
    final products = await _inventoryService.getProducts();
    final categories = await _inventoryService.getCategories();

    // Filtrer les factures payées dans la période
    final paidInvoicesInPeriod =
        invoices
            .where(
              (invoice) =>
                  invoice.status == InvoiceStatus.payee &&
                  invoice.createdAt.isAfter(startDate) &&
                  invoice.createdAt.isBefore(endDate),
            )
            .toList();

    // Map pour stocker les statistiques par produit
    Map<String, Map<String, dynamic>> productStats = {};

    // Analyser chaque facture
    for (final invoice in paidInvoicesInPeriod) {
      for (final item in invoice.items) {
        // Vérifier si le produit existe dans l'inventaire
        final product = products.where((p) => p.id == item.id).firstOrNull;

        // Si le produit n'existe pas dans l'inventaire ou est en rupture de stock
        if (product == null || product.quantity == 0) {
          final productId = item.id;

          if (productStats.containsKey(productId)) {
            // Mettre à jour les statistiques existantes
            productStats[productId]!['totalQuantitySold'] += item.quantity;
            productStats[productId]!['totalRevenue'] += item.total;
            (productStats[productId]!['salesDates'] as List<DateTime>).add(
              invoice.createdAt,
            );
          } else {
            // Créer de nouvelles statistiques
            final categoryName =
                categories
                    .where((c) => c.id == (product?.categoryId ?? ''))
                    .firstOrNull
                    ?.name ??
                item.categoryName;

            productStats[productId] = {
              'productName': item.name,
              'price': item.price,
              'totalQuantitySold': item.quantity,
              'totalRevenue': item.total,
              'salesDates': [invoice.createdAt],
              'categoryName': categoryName,
            };
          }
        }
      }
    }

    // Convertir en liste d'objets OutOfStockProduct
    return productStats.entries.map((entry) {
      final stats = entry.value;
      return OutOfStockProduct(
        productId: entry.key,
        productName: stats['productName'],
        price: stats['price'],
        totalQuantitySold: stats['totalQuantitySold'],
        totalRevenue: stats['totalRevenue'],
        salesDates: List<DateTime>.from(stats['salesDates']),
        categoryName: stats['categoryName'],
      );
    }).toList();
  }

  /// Obtenir le produit hors stock le plus vendu du mois
  static Future<OutOfStockProduct?>
  getMostSoldOutOfStockProductThisMonth() async {
    final outOfStockProducts = await getOutOfStockProductsThisMonth();

    if (outOfStockProducts.isEmpty) return null;

    // Trier par quantité vendue (décroissant)
    outOfStockProducts.sort(
      (a, b) => b.totalQuantitySold.compareTo(a.totalQuantitySold),
    );

    return outOfStockProducts.first;
  }

  /// Obtenir les statistiques générales des produits hors stock
  static Future<Map<String, dynamic>> getOutOfStockStats() async {
    final outOfStockProducts = await getOutOfStockProductsThisMonth();

    final totalProducts = outOfStockProducts.length;
    final totalQuantitySold = outOfStockProducts.fold<int>(
      0,
      (sum, product) => sum + product.totalQuantitySold,
    );
    final totalRevenue = outOfStockProducts.fold<double>(
      0.0,
      (sum, product) => sum + product.totalRevenue,
    );

    final mostSoldProduct = await getMostSoldOutOfStockProductThisMonth();

    return {
      'totalOutOfStockProducts': totalProducts,
      'totalQuantitySold': totalQuantitySold,
      'totalRevenue': totalRevenue,
      'mostSoldProduct': mostSoldProduct,
    };
  }
}
