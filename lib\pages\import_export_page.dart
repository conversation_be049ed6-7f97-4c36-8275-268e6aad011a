import 'dart:convert'; // For utf8 encoding
import 'dart:io'; // For File operations

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart'; // For debugPrint and Uint8List
import 'package:file_picker/file_picker.dart'; // For picking files

import '../models/product.dart';
import '../services/csv_service.dart';
import '../services/excel_service.dart';
import '../services/inventory_service.dart';

class ImportExportPage extends StatefulWidget {
  const ImportExportPage({super.key});

  @override
  State<ImportExportPage> createState() => _ImportExportPageState();
}

class _ImportExportPageState extends State<ImportExportPage>
    with TickerProviderStateMixin {
  final CsvService _csvService = CsvService();
  final ExcelService _excelService = ExcelService();
  final InventoryService _inventoryService = InventoryService.instance;
  String _importStatus = '';
  bool _isExporting = false;
  bool _isImporting = false;

  // Contrôleurs d'animation
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    // Démarrer les animations
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _exportProducts() async {
    setState(() {
      _isExporting = true;
      _importStatus = 'Exportation des produits en cours...';
    });
    try {
      // Corrected method name
      final products = await _inventoryService.getProducts();
      if (products.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Aucun produit à exporter.')),
          );
        }
        return;
      }
      // Corrected method name
      final String csvData = await _csvService.exportProductsToCsv(products);
      final Uint8List bytes = Uint8List.fromList(utf8.encode(csvData));

      String? outputPath = await FilePicker.platform.saveFile(
        dialogTitle: 'Enregistrer le fichier CSV',
        fileName: 'produits.csv',
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (outputPath != null) {
        final file = File(outputPath);
        await file.writeAsBytes(bytes);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Produits exportés avec succès vers $outputPath'),
            ),
          );
          _importStatus = 'Produits exportés avec succès.';
        }
      } else {
        if (mounted) {
          _importStatus = 'Exportation annulée.';
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'exportation : ${e.toString()}'),
          ),
        );
        _importStatus = 'Erreur lors de l\'exportation.';
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  Future<void> _downloadCsvTemplate() async {
    setState(() {
      _isExporting = true;
      _importStatus = 'Téléchargement du modèle CSV...';
    });
    try {
      // Corrected method name
      final String csvTemplate = _csvService.getCsvTemplate();
      final Uint8List bytes = Uint8List.fromList(utf8.encode(csvTemplate));

      String? outputPath = await FilePicker.platform.saveFile(
        dialogTitle: 'Enregistrer le modèle CSV',
        fileName: 'template_produits.csv',
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (outputPath != null) {
        final file = File(outputPath);
        await file.writeAsBytes(bytes);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Modèle CSV téléchargé avec succès vers $outputPath',
              ),
            ),
          );
          _importStatus = 'Modèle CSV téléchargé avec succès.';
        }
      } else {
        if (mounted) {
          _importStatus = 'Téléchargement du modèle annulé.';
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Erreur lors du téléchargement du modèle : ${e.toString()}',
            ),
          ),
        );
        _importStatus = 'Erreur lors du téléchargement du modèle.';
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  // MÉTHODES EXCEL
  Future<void> _exportProductsToExcel() async {
    setState(() {
      _isExporting = true;
      _importStatus = 'Exportation des produits en Excel...';
    });
    try {
      final products = await _inventoryService.getProducts();
      if (products.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Aucun produit à exporter.')),
          );
        }
        return;
      }

      final Uint8List excelBytes = await _excelService.exportProductsToExcel(
        products,
      );

      String? outputPath = await FilePicker.platform.saveFile(
        dialogTitle: 'Enregistrer le fichier Excel',
        fileName: 'produits.xlsx',
        type: FileType.custom,
        allowedExtensions: ['xlsx'],
      );

      if (outputPath != null) {
        final file = File(outputPath);
        await file.writeAsBytes(excelBytes);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Produits exportés avec succès vers $outputPath'),
            ),
          );
          _importStatus = 'Produits exportés en Excel avec succès.';
        }
      } else {
        if (mounted) {
          _importStatus = 'Exportation Excel annulée.';
        }
      }
    } catch (e) {
      debugPrint('Erreur détaillée export Excel: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Erreur lors de l\'exportation Excel : ${e.toString()}',
            ),
          ),
        );
        _importStatus = 'Erreur lors de l\'exportation Excel: ${e.toString()}';
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  Future<void> _downloadExcelTemplate() async {
    setState(() {
      _isExporting = true;
      _importStatus = 'Téléchargement du modèle Excel...';
    });
    try {
      setState(() => _importStatus = 'Génération du modèle Excel...');
      final Uint8List excelTemplate = _excelService.getExcelTemplate();

      setState(() => _importStatus = 'Sélection de l\'emplacement...');
      String? outputPath = await FilePicker.platform.saveFile(
        dialogTitle: 'Enregistrer le modèle Excel',
        fileName: 'template_produits.xlsx',
        type: FileType.custom,
        allowedExtensions: ['xlsx'],
      );

      if (outputPath != null) {
        setState(() => _importStatus = 'Écriture du fichier...');
        final file = File(outputPath);
        await file.writeAsBytes(excelTemplate);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Modèle Excel téléchargé avec succès vers $outputPath',
              ),
            ),
          );
          _importStatus = 'Modèle Excel téléchargé avec succès.';
        }
      } else {
        if (mounted) {
          _importStatus = 'Téléchargement du modèle Excel annulé.';
        }
      }
    } catch (e) {
      debugPrint('Erreur détaillée Excel template: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Erreur lors du téléchargement du modèle Excel : ${e.toString()}',
            ),
          ),
        );
        _importStatus =
            'Erreur lors du téléchargement du modèle Excel: ${e.toString()}';
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }

  Future<void> _importProductsFromExcel() async {
    setState(() {
      _isImporting = true;
      _importStatus = 'Sélection du fichier Excel...';
    });

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() => _importStatus = 'Lecture du fichier Excel...');

        Uint8List fileBytes;
        if (result.files.single.bytes != null) {
          // Web ou mobile - utiliser bytes
          fileBytes = result.files.single.bytes!;
        } else if (result.files.single.path != null) {
          // Desktop - lire le fichier depuis le path
          final file = File(result.files.single.path!);
          fileBytes = await file.readAsBytes();
        } else {
          throw Exception('Impossible de lire le fichier sélectionné');
        }

        setState(() => _importStatus = 'Traitement des données Excel...');
        List<Product> importedProducts = await _excelService
            .importProductsFromExcel(fileBytes);

        if (importedProducts.isEmpty) {
          setState(
            () =>
                _importStatus =
                    'Aucun produit valide trouvé dans le fichier Excel.',
          );
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Aucun produit valide à importer depuis Excel.'),
              ),
            );
          }
        } else {
          setState(
            () => _importStatus = 'Ajout des produits à l\'inventaire...',
          );
          int successCount = 0;
          int errorCount = 0;
          List<String> errorMessages = [];

          for (var product in importedProducts) {
            try {
              await _inventoryService.addProduct(product);
              successCount++;
            } catch (e) {
              errorCount++;
              errorMessages.add(
                'Erreur produit ${product.name}: ${e.toString()}',
              );
              debugPrint(
                'Erreur ajout produit ${product.name}: ${e.toString()}',
              );
            }
          }
          final statusMessage =
              '$successCount produit(s) importé(s) depuis Excel avec succès. $errorCount erreur(s).';
          setState(() => _importStatus = statusMessage);
          if (mounted) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(statusMessage)));
          }

          if (errorCount > 0 && mounted) {
            showDialog(
              context: context,
              builder:
                  (context) => AlertDialog(
                    title: const Text('Erreurs d\'importation Excel'),
                    content: SizedBox(
                      width: double.maxFinite,
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: errorMessages.length,
                        itemBuilder:
                            (context, index) => Text(errorMessages[index]),
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('OK'),
                      ),
                    ],
                  ),
            );
          }
        }
      } else {
        setState(
          () =>
              _importStatus =
                  'Importation Excel annulée ou aucun fichier sélectionné.',
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Aucun fichier Excel sélectionné ou importation annulée.',
              ),
            ),
          );
        }
      }
    } catch (e) {
      final errorMessage = 'Erreur d\'importation Excel: ${e.toString()}';
      setState(() => _importStatus = errorMessage);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(errorMessage)));
      }
      debugPrint(errorMessage);
    } finally {
      if (mounted) {
        setState(() => _isImporting = false);
      }
    }
  }

  Future<void> _importProducts() async {
    setState(() {
      _isImporting = true;
      _importStatus = 'Sélection du fichier...';
    });

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() => _importStatus = 'Lecture du fichier...');

        Uint8List fileBytes;
        if (result.files.single.bytes != null) {
          // Web ou mobile - utiliser bytes
          fileBytes = result.files.single.bytes!;
        } else if (result.files.single.path != null) {
          // Desktop - lire le fichier depuis le path
          final file = File(result.files.single.path!);
          fileBytes = await file.readAsBytes();
        } else {
          throw Exception('Impossible de lire le fichier sélectionné');
        }

        final csvString = utf8.decode(fileBytes);

        setState(() => _importStatus = 'Traitement des données...');
        List<Product> importedProducts = await _csvService
            .importProductsFromCsv(csvString);

        if (importedProducts.isEmpty) {
          setState(
            () =>
                _importStatus =
                    'Aucun produit valide trouvé dans le fichier CSV.',
          );
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Aucun produit valide à importer.')),
            );
          }
        } else {
          setState(
            () => _importStatus = 'Ajout des produits à l\'inventaire...',
          );
          int successCount = 0;
          int errorCount = 0;
          List<String> errorMessages = [];

          for (var product in importedProducts) {
            try {
              await _inventoryService.addProduct(product);
              successCount++;
            } catch (e) {
              errorCount++;
              errorMessages.add(
                'Erreur produit ${product.name}: ${e.toString()}',
              );
              debugPrint(
                'Erreur ajout produit ${product.name}: ${e.toString()}',
              );
            }
          }
          final statusMessage =
              '$successCount produit(s) importé(s) avec succès. $errorCount erreur(s).';
          setState(() => _importStatus = statusMessage);
          if (mounted) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(statusMessage)));
          }

          if (errorCount > 0 && mounted) {
            showDialog(
              context: context,
              builder:
                  (context) => AlertDialog(
                    title: const Text('Erreurs d\'importation'),
                    content: SizedBox(
                      width: double.maxFinite,
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: errorMessages.length,
                        itemBuilder:
                            (context, index) => Text(errorMessages[index]),
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('OK'),
                      ),
                    ],
                  ),
            );
          }
        }
      } else {
        setState(
          () =>
              _importStatus = 'Importation annulée ou fichier non sélectionné.',
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Aucun fichier CSV sélectionné.')),
          );
        }
      }
    } catch (e) {
      final errorMessage = 'Erreur d\'importation: ${e.toString()}';
      setState(() => _importStatus = errorMessage);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(errorMessage)));
      }
      debugPrint(errorMessage);
    } finally {
      if (mounted) {
        setState(() => _isImporting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Import / Export de Données')),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 600),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Exportation',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Exportez tous vos produits au format CSV. Ce fichier peut être utilisé comme sauvegarde ou pour transférer des données.',
                            ),
                            const SizedBox(height: 16),
                            _isExporting
                                ? const Center(
                                  child: CircularProgressIndicator(),
                                )
                                : Column(
                                  children: [
                                    ElevatedButton.icon(
                                      icon: const Icon(
                                        Icons.file_download_outlined,
                                      ),
                                      label: const Text(
                                        'Exporter les Produits en CSV',
                                      ),
                                      onPressed: _exportProducts,
                                      style: ElevatedButton.styleFrom(
                                        minimumSize: const Size(
                                          double.infinity,
                                          48,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 10),
                                    ElevatedButton.icon(
                                      icon: const Icon(
                                        Icons.file_download_outlined,
                                      ),
                                      label: const Text(
                                        'Exporter les Produits en Excel',
                                      ),
                                      onPressed: _exportProductsToExcel,
                                      style: ElevatedButton.styleFrom(
                                        minimumSize: const Size(
                                          double.infinity,
                                          48,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Importation',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'Importez des produits à partir d\'un fichier CSV ou Excel. Assurez-vous que le fichier respecte le format spécifié.',
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    icon: const Icon(
                                      Icons.file_upload_outlined,
                                    ),
                                    label: const Text('Modèle CSV'),
                                    onPressed: _downloadCsvTemplate,
                                    style: ElevatedButton.styleFrom(
                                      minimumSize: const Size(
                                        double.infinity,
                                        48,
                                      ),
                                      backgroundColor: Colors.grey[700],
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    icon: const Icon(
                                      Icons.file_upload_outlined,
                                    ),
                                    label: const Text('Modèle Excel'),
                                    onPressed: _downloadExcelTemplate,
                                    style: ElevatedButton.styleFrom(
                                      minimumSize: const Size(
                                        double.infinity,
                                        48,
                                      ),
                                      backgroundColor: Colors.grey[700],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            _isImporting
                                ? Column(
                                  children: [
                                    const Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      _importStatus,
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                )
                                : Row(
                                  children: [
                                    Expanded(
                                      child: ElevatedButton.icon(
                                        icon: const Icon(
                                          Icons.upload_file_outlined,
                                        ),
                                        label: const Text('Importer CSV'),
                                        onPressed: _importProducts,
                                        style: ElevatedButton.styleFrom(
                                          minimumSize: const Size(
                                            double.infinity,
                                            48,
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    Expanded(
                                      child: ElevatedButton.icon(
                                        icon: const Icon(
                                          Icons.upload_file_outlined,
                                        ),
                                        label: const Text('Importer Excel'),
                                        onPressed: _importProductsFromExcel,
                                        style: ElevatedButton.styleFrom(
                                          minimumSize: const Size(
                                            double.infinity,
                                            48,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                            if (_importStatus.isNotEmpty && !_isImporting)
                              Padding(
                                padding: const EdgeInsets.only(top: 16.0),
                                child: Text(
                                  'Statut: $_importStatus',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
