class Category {
  final String id;
  String name;
  double? defaultPrice;

  Category({
    required this.id,
    required this.name,
    this.defaultPrice,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'defaultPrice': defaultPrice,
      };

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        id: json['id'] as String,
        name: json['name'] as String,
        defaultPrice: json['defaultPrice']?.toDouble(),
      );

  Category copyWith({
    String? id,
    String? name,
    double? defaultPrice,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      defaultPrice: defaultPrice ?? this.defaultPrice,
    );
  }
}