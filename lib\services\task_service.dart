import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:generateur_facture/models/task.dart';

class TaskService {
  static TaskService? _instance;
  static TaskService get instance => _instance ??= TaskService._internal();
  
  TaskService._internal();

  static const String _tasksKey = 'tasks';
  List<Task> _tasks = [];
  bool _isInitialized = false;

  Future<void> _initializeIfNeeded() async {
    if (!_isInitialized) {
      await _loadTasksFromStorage();
      _isInitialized = true;
    }
  }

  Future<void> _loadTasksFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tasksJson = prefs.getString(_tasksKey);
      
      if (tasksJson != null) {
        final List<dynamic> tasksList = json.decode(tasksJson);
        _tasks = tasksList.map((taskJson) => Task.fromJson(taskJson)).toList();
      } else {
        _tasks = [];
      }
    } catch (e) {
      // Log error silently and initialize empty list
      _tasks = [];
    }
  }

  Future<void> _saveTasksToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tasksJson = json.encode(_tasks.map((task) => task.toJson()).toList());
      await prefs.setString(_tasksKey, tasksJson);
    } catch (e) {
      // Re-throw with user-friendly message
      throw Exception('Impossible de sauvegarder les tâches');
    }
  }

  Future<List<Task>> getTasks() async {
    await _initializeIfNeeded();
    // Trier par date d'échéance puis par priorité
    _tasks.sort((a, b) {
      if (a.isCompleted != b.isCompleted) {
        return a.isCompleted ? 1 : -1; // Tâches non terminées en premier
      }
      final dateComparison = a.dueDate.compareTo(b.dueDate);
      if (dateComparison != 0) {
        return dateComparison;
      }
      return b.priority.value.compareTo(a.priority.value); // Priorité élevée en premier
    });
    return List.from(_tasks);
  }

  Future<Task?> getTaskById(String id) async {
    await _initializeIfNeeded();
    try {
      return _tasks.firstWhere((task) => task.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> addTask(Task task) async {
    await _initializeIfNeeded();
    
    // Générer un ID unique
    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final taskWithId = task.copyWith(id: id);
    
    _tasks.add(taskWithId);
    await _saveTasksToStorage();
  }

  Future<void> updateTask(Task updatedTask) async {
    await _initializeIfNeeded();
    
    final index = _tasks.indexWhere((task) => task.id == updatedTask.id);
    if (index != -1) {
      _tasks[index] = updatedTask;
      await _saveTasksToStorage();
    } else {
      throw Exception('Tâche non trouvée');
    }
  }

  Future<void> deleteTask(String id) async {
    await _initializeIfNeeded();
    
    final initialLength = _tasks.length;
    _tasks.removeWhere((task) => task.id == id);
    
    if (_tasks.length == initialLength) {
      throw Exception('Tâche non trouvée');
    }
    
    await _saveTasksToStorage();
  }

  Future<void> markTaskAsCompleted(String id) async {
    await _initializeIfNeeded();
    
    final task = await getTaskById(id);
    if (task != null) {
      await updateTask(task.copyWith(isCompleted: true));
    } else {
      throw Exception('Tâche non trouvée');
    }
  }

  Future<void> markTaskAsIncomplete(String id) async {
    await _initializeIfNeeded();
    
    final task = await getTaskById(id);
    if (task != null) {
      await updateTask(task.copyWith(isCompleted: false));
    } else {
      throw Exception('Tâche non trouvée');
    }
  }

  Future<List<Task>> getTasksByPriority(TaskPriority priority) async {
    final tasks = await getTasks();
    return tasks.where((task) => task.priority == priority).toList();
  }

  Future<List<Task>> getCompletedTasks() async {
    final tasks = await getTasks();
    return tasks.where((task) => task.isCompleted).toList();
  }

  Future<List<Task>> getPendingTasks() async {
    final tasks = await getTasks();
    return tasks.where((task) => !task.isCompleted).toList();
  }

  Future<List<Task>> getOverdueTasks() async {
    final tasks = await getTasks();
    final now = DateTime.now();
    return tasks.where((task) => !task.isCompleted && task.dueDate.isBefore(now)).toList();
  }

  Future<List<Task>> getTasksDueToday() async {
    final tasks = await getTasks();
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    
    return tasks.where((task) => 
      !task.isCompleted && 
      task.dueDate.isAfter(today) && 
      task.dueDate.isBefore(tomorrow)
    ).toList();
  }

  Future<void> clearAllTasks() async {
    await _initializeIfNeeded();
    _tasks.clear();
    await _saveTasksToStorage();
  }

  Future<void> clearCompletedTasks() async {
    await _initializeIfNeeded();
    _tasks.removeWhere((task) => task.isCompleted);
    await _saveTasksToStorage();
  }

  // Statistiques
  Future<Map<String, int>> getTaskStatistics() async {
    final tasks = await getTasks();
    final completed = tasks.where((task) => task.isCompleted).length;
    final pending = tasks.where((task) => !task.isCompleted).length;
    final overdue = await getOverdueTasks();
    final dueToday = await getTasksDueToday();
    
    return {
      'total': tasks.length,
      'completed': completed,
      'pending': pending,
      'overdue': overdue.length,
      'dueToday': dueToday.length,
    };
  }
}