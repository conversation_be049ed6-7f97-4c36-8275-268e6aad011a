import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:workmanager/workmanager.dart';
import 'inventory_service.dart';
import 'task_service.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  static const String stockCheckTaskName = 'stockCheckTask';
  static const String taskReminderTaskName = 'taskReminderTask';

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  /// Initialise le service de notifications
  Future<void> initialize() async {
    await _initializeLocalNotifications();
    await _requestPermissions();
    await _initializeWorkManager();
  }

  /// Initialise les notifications locales
  Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  /// Demande les permissions nécessaires
  Future<void> _requestPermissions() async {
    if (Platform.isAndroid) {
      await Permission.notification.request();
      
      // Pour Android 13+ (API 33+)
      if (await Permission.scheduleExactAlarm.isDenied) {
        await Permission.scheduleExactAlarm.request();
      }
    }

    if (Platform.isIOS) {
      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
    }
  }

  /// Initialise WorkManager pour les tâches en arrière-plan
  Future<void> _initializeWorkManager() async {
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: kDebugMode,
    );
  }

  /// Démarre les notifications périodiques
  Future<void> startPeriodicNotifications() async {
    // Notification pour les stocks toutes les 5 heures
    await Workmanager().registerPeriodicTask(
      stockCheckTaskName,
      stockCheckTaskName,
      frequency: const Duration(hours: 5),
      constraints: Constraints(
        networkType: NetworkType.not_required,
        requiresBatteryNotLow: false,
        requiresCharging: false,
        requiresDeviceIdle: false,
        requiresStorageNotLow: false,
      ),
    );

    // Notification pour les tâches toutes les 30 minutes
    await Workmanager().registerPeriodicTask(
      taskReminderTaskName,
      taskReminderTaskName,
      frequency: const Duration(minutes: 30),
      constraints: Constraints(
        networkType: NetworkType.not_required,
        requiresBatteryNotLow: false,
        requiresCharging: false,
        requiresDeviceIdle: false,
        requiresStorageNotLow: false,
      ),
    );

    debugPrint('Notifications périodiques démarrées');
  }

  /// Arrête les notifications périodiques
  Future<void> stopPeriodicNotifications() async {
    await Workmanager().cancelByUniqueName(stockCheckTaskName);
    await Workmanager().cancelByUniqueName(taskReminderTaskName);
    debugPrint('Notifications périodiques arrêtées');
  }

  /// Vérifie les stocks et envoie une notification si nécessaire
  static Future<void> checkStockLevels() async {
    try {
      final inventoryService = InventoryService.instance;
      final products = await inventoryService.getProducts();
      
      // Produits en rupture de stock (quantité = 0)
      final outOfStockProducts = products.where((p) => p.quantity == 0).toList();
      
      // Produits avec stock faible (quantité <= 5)
      final lowStockProducts = products.where((p) => p.quantity > 0 && p.quantity <= 5).toList();

      if (outOfStockProducts.isNotEmpty || lowStockProducts.isNotEmpty) {
        String title = 'Alerte Stock';
        String body = '';

        if (outOfStockProducts.isNotEmpty) {
          body += '${outOfStockProducts.length} produit(s) en rupture de stock';
        }

        if (lowStockProducts.isNotEmpty) {
          if (body.isNotEmpty) body += '\n';
          body += '${lowStockProducts.length} produit(s) avec stock faible';
        }

        await NotificationService()._showNotification(
          id: 1,
          title: title,
          body: body,
          payload: 'stock_alert',
        );
      }
    } catch (e) {
      debugPrint('Erreur lors de la vérification des stocks: $e');
    }
  }

  /// Vérifie les tâches non terminées et envoie une notification
  static Future<void> checkPendingTasks() async {
    try {
      final taskService = TaskService.instance;
      final tasks = await taskService.getTasks();
      
      // Tâches non terminées
      final pendingTasks = tasks.where((task) => !task.isCompleted).toList();
      
      // Tâches en retard (échéance dépassée)
      final now = DateTime.now();
      final overdueTasks = pendingTasks.where((task) => task.dueDate.isBefore(now)).toList();
      
      // Tâches dues aujourd'hui
      final today = DateTime(now.year, now.month, now.day);
      final tomorrow = today.add(const Duration(days: 1));
      final todayTasks = pendingTasks.where((task) => 
        task.dueDate.isAfter(today) && task.dueDate.isBefore(tomorrow)
      ).toList();

      if (pendingTasks.isNotEmpty) {
        String title = 'Rappel Tâches';
        String body = '';

        if (overdueTasks.isNotEmpty) {
          body += '${overdueTasks.length} tâche(s) en retard';
        }

        if (todayTasks.isNotEmpty) {
          if (body.isNotEmpty) body += '\n';
          body += '${todayTasks.length} tâche(s) à faire aujourd\'hui';
        }

        if (body.isEmpty && pendingTasks.isNotEmpty) {
          body = '${pendingTasks.length} tâche(s) en attente';
        }

        await NotificationService()._showNotification(
          id: 2,
          title: title,
          body: body,
          payload: 'task_reminder',
        );
      }
    } catch (e) {
      debugPrint('Erreur lors de la vérification des tâches: $e');
    }
  }

  /// Affiche une notification
  Future<void> _showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'hcp_design_channel',
      'HCP-DESIGN Notifications',
      channelDescription: 'Notifications pour les stocks et tâches',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      playSound: true,
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );
  }

  /// Gère les clics sur les notifications
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    final payload = notificationResponse.payload;
    debugPrint('Notification cliquée avec payload: $payload');
    
    // Ici vous pouvez naviguer vers la page appropriée
    // selon le payload (stock_alert ou task_reminder)
  }

  /// Envoie une notification de test
  Future<void> sendTestNotification() async {
    await _showNotification(
      id: 999,
      title: 'Test Notification',
      body: 'Ceci est une notification de test pour HCP-DESIGN',
      payload: 'test',
    );
  }
}

/// Callback pour WorkManager (doit être une fonction globale)
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    debugPrint('Exécution de la tâche en arrière-plan: $task');
    
    switch (task) {
      case NotificationService.stockCheckTaskName:
        await NotificationService.checkStockLevels();
        break;
      case NotificationService.taskReminderTaskName:
        await NotificationService.checkPendingTasks();
        break;
    }
    
    return Future.value(true);
  });
}
