import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/ai_models.dart';
import '../models/whatsapp_chat.dart';

class AIService {
  static final AIService _instance = AIService._internal();
  factory AIService() => _instance;
  AIService._internal();

  static const String _configKey = 'ai_configurations';
  static const String _metricsKey = 'ai_performance_metrics';
  static const String _historyKey = 'ai_conversation_history';

  List<AIConfiguration> _configurations = [];
  Map<String, AIPerformanceMetrics> _performanceMetrics = {};
  List<Map<String, dynamic>> _conversationHistory = [];

  /// Initialise le service IA
  Future<void> initialize() async {
    await _loadConfigurations();
    await _loadPerformanceMetrics();
    await _loadConversationHistory();

    // Configurations par défaut si aucune n'existe
    if (_configurations.isEmpty) {
      await _createDefaultConfigurations();
    }
  }

  /// Crée les configurations par défaut
  Future<void> _createDefaultConfigurations() async {
    _configurations = [
      AIConfiguration(
        provider: AIProvider.openai,
        model: AIModel.gpt35Turbo,
        apiKey: '', // À configurer par l'utilisateur
        temperature: 0.7,
        maxTokens: 1000,
        isEnabled: false,
      ),
      AIConfiguration(
        provider: AIProvider.anthropic,
        model: AIModel.claude3Sonnet,
        apiKey: '', // À configurer par l'utilisateur
        temperature: 0.7,
        maxTokens: 1000,
        isEnabled: false,
      ),
      AIConfiguration(
        provider: AIProvider.gemini,
        model: AIModel.geminiPro,
        apiKey: '', // À configurer par l'utilisateur
        temperature: 0.7,
        maxTokens: 1000,
        isEnabled: false,
      ),
      AIConfiguration(
        provider: AIProvider.gemini,
        model: AIModel.gemini15Pro,
        apiKey: '', // À configurer par l'utilisateur
        temperature: 0.7,
        maxTokens: 1000,
        isEnabled: false,
      ),
      AIConfiguration(
        provider: AIProvider.gemini,
        model: AIModel.gemini15Flash,
        apiKey: '', // À configurer par l'utilisateur
        temperature: 0.8, // Température légèrement plus élevée pour Flash
        maxTokens: 2048, // Plus de tokens pour les modèles Flash
        isEnabled: false,
      ),
      AIConfiguration(
        provider: AIProvider.gemini,
        model: AIModel.gemini25Flash,
        apiKey: '', // À configurer par l'utilisateur
        temperature: 0.8, // Température légèrement plus élevée pour Flash
        maxTokens: 2048, // Plus de tokens pour les modèles Flash
        isEnabled: false,
      ),
      AIConfiguration(
        provider: AIProvider.gemma,
        model: AIModel.gemma3_8b,
        apiKey: '', // À configurer par l'utilisateur (Hugging Face Token)
        baseUrl: 'https://api-inference.huggingface.co/models',
        temperature: 0.7,
        maxTokens: 1000,
        isEnabled: false,
      ),
      AIConfiguration(
        provider: AIProvider.mistral,
        model: AIModel.mistralMedium,
        apiKey: '', // À configurer par l'utilisateur
        temperature: 0.7,
        maxTokens: 1000,
        isEnabled: false,
      ),
      AIConfiguration(
        provider: AIProvider.deepseek,
        model: AIModel.deepseekV3,
        apiKey: '', // À configurer par l'utilisateur
        baseUrl: 'https://api.deepseek.com/v1',
        temperature: 0.7,
        maxTokens: 1000,
        isEnabled: false,
      ),
    ];
    await _saveConfigurations();
  }

  /// Génère une réponse IA pour un message
  Future<AIResponse?> generateResponse({
    required String message,
    required String context,
    CustomerIntention? intention,
    List<WhatsAppMessage>? conversationHistory,
    AIModel? preferredModel,
  }) async {
    final config = _getActiveConfiguration(preferredModel);
    if (config == null) {
      debugPrint('Aucune configuration IA active trouvée');
      return null;
    }

    final startTime = DateTime.now();

    try {
      final prompt = _buildPrompt(
        message: message,
        context: context,
        intention: intention,
        conversationHistory: conversationHistory,
      );

      final response = await _callAIProvider(config, prompt);

      final endTime = DateTime.now();
      final responseTime =
          endTime.difference(startTime).inMilliseconds.toDouble();

      // Mettre à jour les métriques
      await _updatePerformanceMetrics(
        config.model.modelName,
        responseTime,
        response != null,
        response?.tokensUsed ?? 0,
        response?.confidence ?? 0.0,
      );

      // Sauvegarder dans l'historique
      await _saveToHistory(message, response?.content ?? '', config.model);

      return response;
    } catch (e) {
      debugPrint('Erreur lors de la génération de réponse IA: $e');

      // Mettre à jour les métriques d'échec
      await _updatePerformanceMetrics(
        config.model.modelName,
        DateTime.now().difference(startTime).inMilliseconds.toDouble(),
        false,
        0,
        0.0,
      );

      return null;
    }
  }

  /// Appelle le fournisseur IA approprié
  Future<AIResponse?> _callAIProvider(
    AIConfiguration config,
    String prompt,
  ) async {
    switch (config.provider) {
      case AIProvider.openai:
        return await _callOpenAI(config, prompt);
      case AIProvider.anthropic:
        return await _callAnthropic(config, prompt);
      case AIProvider.gemini:
        return await _callGemini(config, prompt);
      case AIProvider.gemma:
        return await _callGemma(config, prompt);
      case AIProvider.mistral:
        return await _callMistral(config, prompt);
      case AIProvider.deepseek:
        return await _callDeepSeek(config, prompt);
    }
  }

  /// Appelle l'API OpenAI
  Future<AIResponse?> _callOpenAI(AIConfiguration config, String prompt) async {
    if (config.apiKey.isEmpty) return null;

    final response = await http.post(
      Uri.parse('https://api.openai.com/v1/chat/completions'),
      headers: {
        'Authorization': 'Bearer ${config.apiKey}',
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'model': config.model.modelName,
        'messages': [
          {'role': 'user', 'content': prompt},
        ],
        'temperature': config.temperature,
        'max_tokens': config.maxTokens,
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final content = data['choices'][0]['message']['content'];
      final tokensUsed = data['usage']['total_tokens'];

      return AIResponse(
        id: data['id'],
        content: content,
        model: config.model,
        timestamp: DateTime.now(),
        tokensUsed: tokensUsed,
        confidence: 0.8, // Estimation
      );
    }

    return null;
  }

  /// Appelle l'API Anthropic
  Future<AIResponse?> _callAnthropic(
    AIConfiguration config,
    String prompt,
  ) async {
    if (config.apiKey.isEmpty) return null;

    final response = await http.post(
      Uri.parse('https://api.anthropic.com/v1/messages'),
      headers: {
        'x-api-key': config.apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
      },
      body: jsonEncode({
        'model': config.model.modelName,
        'max_tokens': config.maxTokens,
        'messages': [
          {'role': 'user', 'content': prompt},
        ],
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final content = data['content'][0]['text'];
      final tokensUsed =
          data['usage']['output_tokens'] + data['usage']['input_tokens'];

      return AIResponse(
        id: data['id'],
        content: content,
        model: config.model,
        timestamp: DateTime.now(),
        tokensUsed: tokensUsed,
        confidence: 0.85, // Estimation
      );
    }

    return null;
  }

  /// Appelle l'API Gemini
  Future<AIResponse?> _callGemini(AIConfiguration config, String prompt) async {
    if (config.apiKey.isEmpty) return null;

    final response = await http.post(
      Uri.parse(
        'https://generativelanguage.googleapis.com/v1beta/models/${config.model.modelName}:generateContent?key=${config.apiKey}',
      ),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'contents': [
          {
            'parts': [
              {'text': prompt},
            ],
          },
        ],
        'generationConfig': {
          'temperature': config.temperature,
          'maxOutputTokens': config.maxTokens,
        },
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final content = data['candidates'][0]['content']['parts'][0]['text'];
      final tokensUsed = data['usageMetadata']['totalTokenCount'] ?? 0;

      return AIResponse(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: content,
        model: config.model,
        timestamp: DateTime.now(),
        tokensUsed: tokensUsed,
        confidence: 0.8, // Estimation
      );
    }

    return null;
  }

  /// Appelle l'API Mistral
  Future<AIResponse?> _callMistral(
    AIConfiguration config,
    String prompt,
  ) async {
    if (config.apiKey.isEmpty) return null;

    final response = await http.post(
      Uri.parse('https://api.mistral.ai/v1/chat/completions'),
      headers: {
        'Authorization': 'Bearer ${config.apiKey}',
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'model': config.model.modelName,
        'messages': [
          {'role': 'user', 'content': prompt},
        ],
        'temperature': config.temperature,
        'max_tokens': config.maxTokens,
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final content = data['choices'][0]['message']['content'];
      final tokensUsed = data['usage']['total_tokens'];

      return AIResponse(
        id: data['id'],
        content: content,
        model: config.model,
        timestamp: DateTime.now(),
        tokensUsed: tokensUsed,
        confidence: 0.75, // Estimation
      );
    }

    return null;
  }

  /// Appelle l'API DeepSeek
  Future<AIResponse?> _callDeepSeek(
    AIConfiguration config,
    String prompt,
  ) async {
    if (config.apiKey.isEmpty) return null;

    final baseUrl = config.baseUrl ?? 'https://api.deepseek.com/v1';

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/chat/completions'),
        headers: {
          'Authorization': 'Bearer ${config.apiKey}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'model': config.model.modelName,
          'messages': [
            {
              'role': 'system',
              'content':
                  'Tu es un assistant IA spécialisé dans le service client pour HCP-DESIGN, une entreprise de vente d\'électronique. Réponds de manière professionnelle et utile.',
            },
            {'role': 'user', 'content': prompt},
          ],
          'temperature': config.temperature,
          'max_tokens': config.maxTokens,
          'stream': false,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'];
        final tokensUsed = data['usage']?['total_tokens'] ?? 0;

        return AIResponse(
          id: data['id'] ?? 'deepseek_${DateTime.now().millisecondsSinceEpoch}',
          content: content.trim(),
          model: config.model,
          timestamp: DateTime.now(),
          tokensUsed: tokensUsed,
          confidence: 0.85, // DeepSeek V3 est très performant
        );
      } else {
        debugPrint(
          'Erreur DeepSeek: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      debugPrint('Erreur DeepSeek: $e');
    }

    return null;
  }

  /// Appelle l'API Gemma (via Hugging Face ou Ollama)
  Future<AIResponse?> _callGemma(AIConfiguration config, String prompt) async {
    if (config.apiKey.isEmpty) return null;

    // Gemma peut être hébergé sur Hugging Face ou Ollama
    final baseUrl =
        config.baseUrl ?? 'https://api-inference.huggingface.co/models';

    // Mapping des modèles Gemma 3
    String modelPath;
    switch (config.model.modelName) {
      case 'gemma-3-8b-it':
        modelPath =
            'google/gemma-2-9b-it'; // Utiliser Gemma 2 en attendant Gemma 3
        break;
      case 'gemma-3-27b-it':
        modelPath = 'google/gemma-2-27b-it';
        break;
      case 'gemma-3-70b-it':
        modelPath = 'google/gemma-2-27b-it'; // Fallback vers 27b
        break;
      default:
        modelPath = 'google/${config.model.modelName}';
    }

    try {
      // Construire un prompt optimisé pour Gemma
      final optimizedPrompt = '''<start_of_turn>user
Tu es un assistant IA spécialisé dans le service client pour HCP-DESIGN, une entreprise de vente d'électronique au Sénégal. Réponds de manière professionnelle, utile et en français.

Question du client: $prompt
<end_of_turn>
<start_of_turn>model
''';

      final response = await http.post(
        Uri.parse('$baseUrl/$modelPath'),
        headers: {
          'Authorization': 'Bearer ${config.apiKey}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'inputs': optimizedPrompt,
          'parameters': {
            'temperature': config.temperature,
            'max_new_tokens': config.maxTokens,
            'return_full_text': false,
            'do_sample': true,
            'top_p': 0.9,
            'stop': ['<end_of_turn>'],
          },
          'options': {'wait_for_model': true, 'use_cache': false},
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        String content = '';

        if (data is List && data.isNotEmpty) {
          content = data[0]['generated_text'] ?? '';
        } else if (data is Map && data['generated_text'] != null) {
          content = data['generated_text'];
        }

        // Nettoyer la réponse
        content = content.trim();
        if (content.contains('<end_of_turn>')) {
          content = content.split('<end_of_turn>')[0].trim();
        }

        return AIResponse(
          id: 'gemma_${DateTime.now().millisecondsSinceEpoch}',
          content: content,
          model: config.model,
          timestamp: DateTime.now(),
          tokensUsed: content.split(' ').length, // Estimation
          confidence: 0.8, // Gemma est performant
        );
      } else {
        debugPrint('Erreur Gemma: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('Erreur Gemma: $e');
    }

    return null;
  }

  /// Construit le prompt pour l'IA
  String _buildPrompt({
    required String message,
    required String context,
    CustomerIntention? intention,
    List<WhatsAppMessage>? conversationHistory,
  }) {
    final buffer = StringBuffer();

    buffer.writeln(
      'Tu es un assistant IA pour HCP-DESIGN, une application de gestion commerciale.',
    );
    buffer.writeln('Contexte: $context');

    if (intention != null) {
      buffer.writeln('Intention détectée du client: ${intention.value}');
    }

    if (conversationHistory != null && conversationHistory.isNotEmpty) {
      buffer.writeln('\nHistorique de conversation récent:');
      for (final msg in conversationHistory.take(5)) {
        final sender = msg.isFromCustomer ? 'Client' : 'Assistant';
        buffer.writeln('$sender: ${msg.content}');
      }
    }

    buffer.writeln('\nMessage du client: $message');
    buffer.writeln(
      '\nRéponds de manière professionnelle, utile et adaptée au contexte commercial.',
    );

    return buffer.toString();
  }

  /// Obtient la configuration active
  AIConfiguration? _getActiveConfiguration(AIModel? preferredModel) {
    if (preferredModel != null) {
      try {
        final config = _configurations.firstWhere(
          (c) =>
              c.model == preferredModel && c.isEnabled && c.apiKey.isNotEmpty,
        );
        return config;
      } catch (e) {
        // Continue vers la configuration par défaut
      }
    }

    try {
      return _configurations.firstWhere(
        (c) => c.isEnabled && c.apiKey.isNotEmpty,
      );
    } catch (e) {
      return null;
    }
  }

  /// Met à jour les métriques de performance
  Future<void> _updatePerformanceMetrics(
    String modelName,
    double responseTime,
    bool success,
    int tokensUsed,
    double confidence,
  ) async {
    final metrics =
        _performanceMetrics[modelName] ??
        AIPerformanceMetrics(
          modelName: modelName,
          totalRequests: 0,
          successfulRequests: 0,
          failedRequests: 0,
          averageResponseTime: 0.0,
          averageConfidence: 0.0,
          totalTokensUsed: 0,
          lastUsed: DateTime.now(),
        );

    final newTotalRequests = metrics.totalRequests + 1;
    final newSuccessfulRequests =
        success ? metrics.successfulRequests + 1 : metrics.successfulRequests;
    final newFailedRequests =
        success ? metrics.failedRequests : metrics.failedRequests + 1;

    final newAverageResponseTime =
        ((metrics.averageResponseTime * metrics.totalRequests) + responseTime) /
        newTotalRequests;
    final newAverageConfidence =
        success
            ? ((metrics.averageConfidence * metrics.successfulRequests) +
                    confidence) /
                newSuccessfulRequests
            : metrics.averageConfidence;

    _performanceMetrics[modelName] = AIPerformanceMetrics(
      modelName: modelName,
      totalRequests: newTotalRequests,
      successfulRequests: newSuccessfulRequests,
      failedRequests: newFailedRequests,
      averageResponseTime: newAverageResponseTime,
      averageConfidence: newAverageConfidence,
      totalTokensUsed: metrics.totalTokensUsed + tokensUsed,
      lastUsed: DateTime.now(),
      intentionAccuracy: metrics.intentionAccuracy,
    );

    await _savePerformanceMetrics();
  }

  /// Sauvegarde dans l'historique
  Future<void> _saveToHistory(
    String userMessage,
    String aiResponse,
    AIModel model,
  ) async {
    _conversationHistory.add({
      'timestamp': DateTime.now().toIso8601String(),
      'userMessage': userMessage,
      'aiResponse': aiResponse,
      'model': model.modelName,
    });

    // Garder seulement les 1000 dernières conversations
    if (_conversationHistory.length > 1000) {
      _conversationHistory = _conversationHistory.sublist(
        _conversationHistory.length - 1000,
      );
    }

    await _saveConversationHistory();
  }

  // Méthodes de persistance
  Future<void> _loadConfigurations() async {
    final prefs = await SharedPreferences.getInstance();
    final configsJson = prefs.getString(_configKey);
    if (configsJson != null) {
      final configsList = jsonDecode(configsJson) as List;
      _configurations =
          configsList.map((c) => AIConfiguration.fromJson(c)).toList();
    }
  }

  Future<void> _saveConfigurations() async {
    final prefs = await SharedPreferences.getInstance();
    final configsJson = jsonEncode(
      _configurations.map((c) => c.toJson()).toList(),
    );
    await prefs.setString(_configKey, configsJson);
  }

  Future<void> _loadPerformanceMetrics() async {
    final prefs = await SharedPreferences.getInstance();
    final metricsJson = prefs.getString(_metricsKey);
    if (metricsJson != null) {
      final metricsMap = jsonDecode(metricsJson) as Map<String, dynamic>;
      _performanceMetrics = metricsMap.map(
        (key, value) => MapEntry(key, AIPerformanceMetrics.fromJson(value)),
      );
    }
  }

  Future<void> _savePerformanceMetrics() async {
    final prefs = await SharedPreferences.getInstance();
    final metricsJson = jsonEncode(
      _performanceMetrics.map((key, value) => MapEntry(key, value.toJson())),
    );
    await prefs.setString(_metricsKey, metricsJson);
  }

  Future<void> _loadConversationHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getString(_historyKey);
    if (historyJson != null) {
      _conversationHistory = List<Map<String, dynamic>>.from(
        jsonDecode(historyJson),
      );
    }
  }

  Future<void> _saveConversationHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = jsonEncode(_conversationHistory);
    await prefs.setString(_historyKey, historyJson);
  }

  // Getters publics
  List<AIConfiguration> get configurations => List.from(_configurations);
  Map<String, AIPerformanceMetrics> get performanceMetrics =>
      Map.from(_performanceMetrics);
  List<Map<String, dynamic>> get conversationHistory =>
      List.from(_conversationHistory);

  /// Met à jour une configuration
  Future<void> updateConfiguration(AIConfiguration config) async {
    final index = _configurations.indexWhere(
      (c) => c.provider == config.provider && c.model == config.model,
    );
    if (index != -1) {
      _configurations[index] = config;
    } else {
      _configurations.add(config);
    }
    await _saveConfigurations();
  }

  /// Supprime une configuration
  Future<void> removeConfiguration(AIProvider provider, AIModel model) async {
    _configurations.removeWhere(
      (c) => c.provider == provider && c.model == model,
    );
    await _saveConfigurations();
  }
}
