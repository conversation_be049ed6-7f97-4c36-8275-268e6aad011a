enum AIProvider {
  openai('OpenAI'),
  anthropic('Anthropic'),
  gemini('Gemini'),
  gemma('<PERSON>'),
  mistral('<PERSON><PERSON><PERSON>'),
  deepseek('DeepSeek');

  const AIProvider(this.displayName);
  final String displayName;
}

enum AIModel {
  gpt35Turbo('gpt-3.5-turbo', AIProvider.openai),
  gpt4('gpt-4', AIProvider.openai),
  gpt4<PERSON><PERSON><PERSON>('gpt-4-turbo', AIProvider.openai),
  claude3Sonnet('claude-3-sonnet-20240229', AIProvider.anthropic),
  claude3<PERSON><PERSON><PERSON>('claude-3-haiku-20240307', AIProvider.anthropic),
  claude35Sonnet('claude-3-5-sonnet-20241022', AIProvider.anthropic),
  geminiPro('gemini-pro', AIProvider.gemini),
  gemini15Pro('gemini-1.5-pro', AIProvider.gemini),
  gemini15Flash('gemini-1.5-flash', AIProvider.gemini),
  gemini25Flash('gemini-2.5-flash', AIProvider.gemini),
  gemma3_8b('gemma-3-8b-it', AIProvider.gemma),
  gemma3_27b('gemma-3-27b-it', AIProvider.gemma),
  gemma3_70b('gemma-3-70b-it', AIProvider.gemma),
  mistralMedium('mistral-medium', AIProvider.mistral),
  mistralLarge('mistral-large-latest', AIProvider.mistral),
  deepseekChat('deepseek-chat', AIProvider.deepseek),
  deepseekCoder('deepseek-coder', AIProvider.deepseek),
  deepseekV3('deepseek-v3', AIProvider.deepseek);

  const AIModel(this.modelName, this.provider);
  final String modelName;
  final AIProvider provider;
}

class AIConfiguration {
  final AIProvider provider;
  final AIModel model;
  final String apiKey;
  final String? baseUrl;
  final double temperature;
  final int maxTokens;
  final bool isEnabled;
  final Map<String, dynamic> additionalParams;

  AIConfiguration({
    required this.provider,
    required this.model,
    required this.apiKey,
    this.baseUrl,
    this.temperature = 0.7,
    this.maxTokens = 1000,
    this.isEnabled = true,
    this.additionalParams = const {},
  });

  Map<String, dynamic> toJson() => {
    'provider': provider.name,
    'model': model.modelName,
    'apiKey': apiKey,
    'baseUrl': baseUrl,
    'temperature': temperature,
    'maxTokens': maxTokens,
    'isEnabled': isEnabled,
    'additionalParams': additionalParams,
  };

  factory AIConfiguration.fromJson(Map<String, dynamic> json) =>
      AIConfiguration(
        provider: AIProvider.values.firstWhere(
          (e) => e.name == json['provider'],
        ),
        model: AIModel.values.firstWhere((e) => e.modelName == json['model']),
        apiKey: json['apiKey'],
        baseUrl: json['baseUrl'],
        temperature: json['temperature']?.toDouble() ?? 0.7,
        maxTokens: json['maxTokens'] ?? 1000,
        isEnabled: json['isEnabled'] ?? true,
        additionalParams: json['additionalParams'] ?? {},
      );
}

class AIResponse {
  final String id;
  final String content;
  final AIModel model;
  final DateTime timestamp;
  final int tokensUsed;
  final double confidence;
  final Map<String, dynamic> metadata;
  final String? error;

  AIResponse({
    required this.id,
    required this.content,
    required this.model,
    required this.timestamp,
    required this.tokensUsed,
    required this.confidence,
    this.metadata = const {},
    this.error,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'content': content,
    'model': model.modelName,
    'timestamp': timestamp.toIso8601String(),
    'tokensUsed': tokensUsed,
    'confidence': confidence,
    'metadata': metadata,
    'error': error,
  };

  factory AIResponse.fromJson(Map<String, dynamic> json) => AIResponse(
    id: json['id'],
    content: json['content'],
    model: AIModel.values.firstWhere((e) => e.modelName == json['model']),
    timestamp: DateTime.parse(json['timestamp']),
    tokensUsed: json['tokensUsed'],
    confidence: json['confidence']?.toDouble() ?? 0.0,
    metadata: json['metadata'] ?? {},
    error: json['error'],
  );
}

class KnowledgeArticle {
  final String id;
  final String title;
  final String content;
  final List<String> tags;
  final List<String> keywords;
  final String category;
  final double relevanceScore;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  KnowledgeArticle({
    required this.id,
    required this.title,
    required this.content,
    required this.tags,
    required this.keywords,
    required this.category,
    this.relevanceScore = 0.0,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'content': content,
    'tags': tags,
    'keywords': keywords,
    'category': category,
    'relevanceScore': relevanceScore,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'isActive': isActive,
  };

  factory KnowledgeArticle.fromJson(Map<String, dynamic> json) =>
      KnowledgeArticle(
        id: json['id'],
        title: json['title'],
        content: json['content'],
        tags: List<String>.from(json['tags'] ?? []),
        keywords: List<String>.from(json['keywords'] ?? []),
        category: json['category'],
        relevanceScore: json['relevanceScore']?.toDouble() ?? 0.0,
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        isActive: json['isActive'] ?? true,
      );
}

class QuoteRequest {
  final String id;
  final String chatId;
  final String customerId;
  final List<QuoteItem> items;
  final String status;
  final double totalAmount;
  final String currency;
  final DateTime validUntil;
  final String? notes;
  final DateTime createdAt;
  final Map<String, dynamic> metadata;

  QuoteRequest({
    required this.id,
    required this.chatId,
    required this.customerId,
    required this.items,
    required this.status,
    required this.totalAmount,
    this.currency = 'FCFA',
    required this.validUntil,
    this.notes,
    required this.createdAt,
    this.metadata = const {},
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'chatId': chatId,
    'customerId': customerId,
    'items': items.map((i) => i.toJson()).toList(),
    'status': status,
    'totalAmount': totalAmount,
    'currency': currency,
    'validUntil': validUntil.toIso8601String(),
    'notes': notes,
    'createdAt': createdAt.toIso8601String(),
    'metadata': metadata,
  };

  factory QuoteRequest.fromJson(Map<String, dynamic> json) => QuoteRequest(
    id: json['id'],
    chatId: json['chatId'],
    customerId: json['customerId'],
    items: (json['items'] as List).map((i) => QuoteItem.fromJson(i)).toList(),
    status: json['status'],
    totalAmount: json['totalAmount']?.toDouble() ?? 0.0,
    currency: json['currency'] ?? 'FCFA',
    validUntil: DateTime.parse(json['validUntil']),
    notes: json['notes'],
    createdAt: DateTime.parse(json['createdAt']),
    metadata: json['metadata'] ?? {},
  );
}

class QuoteItem {
  final String productId;
  final String productName;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final String? description;

  QuoteItem({
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.description,
  });

  Map<String, dynamic> toJson() => {
    'productId': productId,
    'productName': productName,
    'quantity': quantity,
    'unitPrice': unitPrice,
    'totalPrice': totalPrice,
    'description': description,
  };

  factory QuoteItem.fromJson(Map<String, dynamic> json) => QuoteItem(
    productId: json['productId'],
    productName: json['productName'],
    quantity: json['quantity'],
    unitPrice: json['unitPrice']?.toDouble() ?? 0.0,
    totalPrice: json['totalPrice']?.toDouble() ?? 0.0,
    description: json['description'],
  );
}

class AIPerformanceMetrics {
  final String modelName;
  final int totalRequests;
  final int successfulRequests;
  final int failedRequests;
  final double averageResponseTime;
  final double averageConfidence;
  final int totalTokensUsed;
  final DateTime lastUsed;
  final Map<String, int> intentionAccuracy;

  AIPerformanceMetrics({
    required this.modelName,
    required this.totalRequests,
    required this.successfulRequests,
    required this.failedRequests,
    required this.averageResponseTime,
    required this.averageConfidence,
    required this.totalTokensUsed,
    required this.lastUsed,
    this.intentionAccuracy = const {},
  });

  double get successRate =>
      totalRequests > 0 ? successfulRequests / totalRequests : 0.0;

  Map<String, dynamic> toJson() => {
    'modelName': modelName,
    'totalRequests': totalRequests,
    'successfulRequests': successfulRequests,
    'failedRequests': failedRequests,
    'averageResponseTime': averageResponseTime,
    'averageConfidence': averageConfidence,
    'totalTokensUsed': totalTokensUsed,
    'lastUsed': lastUsed.toIso8601String(),
    'intentionAccuracy': intentionAccuracy,
  };

  factory AIPerformanceMetrics.fromJson(Map<String, dynamic> json) =>
      AIPerformanceMetrics(
        modelName: json['modelName'],
        totalRequests: json['totalRequests'],
        successfulRequests: json['successfulRequests'],
        failedRequests: json['failedRequests'],
        averageResponseTime: json['averageResponseTime']?.toDouble() ?? 0.0,
        averageConfidence: json['averageConfidence']?.toDouble() ?? 0.0,
        totalTokensUsed: json['totalTokensUsed'],
        lastUsed: DateTime.parse(json['lastUsed']),
        intentionAccuracy: Map<String, int>.from(
          json['intentionAccuracy'] ?? {},
        ),
      );
}
