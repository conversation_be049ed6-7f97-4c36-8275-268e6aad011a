import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:network_info_plus/network_info_plus.dart';
import 'backup_service.dart';

class LocalSyncService {
  static final LocalSyncService _instance = LocalSyncService._internal();
  factory LocalSyncService() => _instance;
  LocalSyncService._internal();

  static const int _serverPort = 8080;
  static const String _syncEndpoint = '/sync';
  static const String _discoveryEndpoint = '/discover';

  HttpServer? _server;
  bool _isServerRunning = false;
  String? _localIP;
  final List<String> _discoveredDevices = [];

  /// Démarre le serveur de synchronisation sur cet appareil
  Future<bool> startSyncServer() async {
    try {
      _localIP = await _getLocalIP();
      if (_localIP == null) {
        debugPrint('Impossible d\'obtenir l\'adresse IP locale');
        return false;
      }

      _server = await HttpServer.bind(InternetAddress.anyIPv4, _serverPort);
      _isServerRunning = true;

      debugPrint(
        'Serveur de synchronisation démarré sur $_localIP:$_serverPort',
      );

      _server!.listen((HttpRequest request) async {
        await _handleRequest(request);
      });

      return true;
    } catch (e) {
      debugPrint('Erreur lors du démarrage du serveur: $e');
      return false;
    }
  }

  /// Arrête le serveur de synchronisation
  Future<void> stopSyncServer() async {
    if (_server != null) {
      await _server!.close();
      _server = null;
      _isServerRunning = false;
      debugPrint('Serveur de synchronisation arrêté');
    }
  }

  /// Gère les requêtes HTTP entrantes
  Future<void> _handleRequest(HttpRequest request) async {
    // Ajouter les en-têtes CORS pour permettre les requêtes cross-origin
    request.response.headers.add('Access-Control-Allow-Origin', '*');
    request.response.headers.add(
      'Access-Control-Allow-Methods',
      'GET, POST, OPTIONS',
    );
    request.response.headers.add(
      'Access-Control-Allow-Headers',
      'Content-Type',
    );

    if (request.method == 'OPTIONS') {
      request.response.statusCode = 200;
      await request.response.close();
      return;
    }

    try {
      switch (request.uri.path) {
        case _discoveryEndpoint:
          await _handleDiscovery(request);
          break;
        case _syncEndpoint:
          if (request.method == 'GET') {
            await _handleSyncRequest(request);
          } else if (request.method == 'POST') {
            await _handleSyncReceive(request);
          }
          break;
        default:
          request.response.statusCode = 404;
          request.response.write('Endpoint non trouvé');
      }
    } catch (e) {
      debugPrint('Erreur lors du traitement de la requête: $e');
      request.response.statusCode = 500;
      request.response.write('Erreur serveur: $e');
    }

    await request.response.close();
  }

  /// Gère la découverte d'appareils
  Future<void> _handleDiscovery(HttpRequest request) async {
    final deviceInfo = {
      'device_name': await _getDeviceName(),
      'ip': _localIP,
      'port': _serverPort,
      'timestamp': DateTime.now().toIso8601String(),
      'app_version': '1.0.0',
    };

    request.response.headers.contentType = ContentType.json;
    request.response.write(jsonEncode(deviceInfo));
  }

  /// Gère les demandes de synchronisation (envoi des données)
  Future<void> _handleSyncRequest(HttpRequest request) async {
    try {
      // Créer une sauvegarde complète
      final backupData = await BackupService.createBackup();

      request.response.headers.contentType = ContentType.json;
      request.response.write(
        jsonEncode({
          'status': 'success',
          'data': backupData,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );
    } catch (e) {
      request.response.statusCode = 500;
      request.response.write(
        jsonEncode({'status': 'error', 'message': e.toString()}),
      );
    }
  }

  /// Gère la réception de données de synchronisation
  Future<void> _handleSyncReceive(HttpRequest request) async {
    try {
      final body = await utf8.decoder.bind(request).join();
      final data = jsonDecode(body);

      // Restaurer les données reçues
      await BackupService.restoreFromBackup(data);

      request.response.headers.contentType = ContentType.json;
      request.response.write(
        jsonEncode({
          'status': 'success',
          'message': 'Synchronisation réussie',
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );
    } catch (e) {
      request.response.statusCode = 500;
      request.response.write(
        jsonEncode({'status': 'error', 'message': e.toString()}),
      );
    }
  }

  /// Découvre les appareils sur le réseau local
  Future<List<Map<String, dynamic>>> discoverDevices() async {
    _discoveredDevices.clear();
    final devices = <Map<String, dynamic>>[];

    try {
      final localIP = await _getLocalIP();
      if (localIP == null) return devices;

      // Extraire le sous-réseau (ex: 192.168.1.x)
      final subnet = localIP.substring(0, localIP.lastIndexOf('.'));

      // Scanner les adresses IP du sous-réseau (1-254)
      final futures = <Future>[];

      for (int i = 1; i <= 254; i++) {
        final ip = '$subnet.$i';
        if (ip == localIP) continue; // Ignorer sa propre IP

        futures.add(_checkDevice(ip));
      }

      await Future.wait(futures);

      // Récupérer les informations des appareils découverts
      for (final ip in _discoveredDevices) {
        try {
          final deviceInfo = await _getDeviceInfo(ip);
          if (deviceInfo != null) {
            devices.add(deviceInfo);
          }
        } catch (e) {
          debugPrint('Erreur lors de la récupération des infos de $ip: $e');
        }
      }
    } catch (e) {
      debugPrint('Erreur lors de la découverte: $e');
    }

    return devices;
  }

  /// Vérifie si un appareil répond sur une IP donnée
  Future<void> _checkDevice(String ip) async {
    try {
      final response = await http
          .get(
            Uri.parse('http://$ip:$_serverPort$_discoveryEndpoint'),
            headers: {'Connection': 'close'},
          )
          .timeout(const Duration(seconds: 2));

      if (response.statusCode == 200) {
        _discoveredDevices.add(ip);
      }
    } catch (e) {
      // Ignorer les erreurs (appareil non disponible)
    }
  }

  /// Récupère les informations d'un appareil
  Future<Map<String, dynamic>?> _getDeviceInfo(String ip) async {
    try {
      final response = await http
          .get(
            Uri.parse('http://$ip:$_serverPort$_discoveryEndpoint'),
            headers: {'Connection': 'close'},
          )
          .timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
    } catch (e) {
      debugPrint('Erreur lors de la récupération des infos de $ip: $e');
    }
    return null;
  }

  /// Synchronise avec un appareil distant
  Future<bool> syncWithDevice(String targetIP) async {
    try {
      // 1. Récupérer les données de l'appareil distant
      final response = await http
          .get(
            Uri.parse('http://$targetIP:$_serverPort$_syncEndpoint'),
            headers: {'Connection': 'close'},
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode != 200) {
        debugPrint(
          'Erreur lors de la récupération des données: ${response.statusCode}',
        );
        return false;
      }

      final remoteData = jsonDecode(response.body);

      // 2. Restaurer les données localement
      await BackupService.restoreFromBackup(remoteData);

      // 3. Envoyer nos données à l'appareil distant
      final localData = await BackupService.createBackup();

      final sendResponse = await http
          .post(
            Uri.parse('http://$targetIP:$_serverPort$_syncEndpoint'),
            headers: {
              'Content-Type': 'application/json',
              'Connection': 'close',
            },
            body: jsonEncode({
              'data': localData,
              'timestamp': DateTime.now().toIso8601String(),
            }),
          )
          .timeout(const Duration(seconds: 30));

      return sendResponse.statusCode == 200;
    } catch (e) {
      debugPrint('Erreur lors de la synchronisation: $e');
      return false;
    }
  }

  /// Obtient l'adresse IP locale
  Future<String?> _getLocalIP() async {
    try {
      final info = NetworkInfo();
      return await info.getWifiIP();
    } catch (e) {
      debugPrint('Erreur lors de l\'obtention de l\'IP: $e');
      return null;
    }
  }

  /// Obtient le nom de l'appareil
  Future<String> _getDeviceName() async {
    try {
      final info = NetworkInfo();
      final wifiName = await info.getWifiName();
      return 'HCP-DESIGN-${wifiName ?? 'Device'}';
    } catch (e) {
      return 'HCP-DESIGN-Device';
    }
  }

  // Getters
  bool get isServerRunning => _isServerRunning;
  String? get localIP => _localIP;
  List<String> get discoveredDevices => List.from(_discoveredDevices);
}
