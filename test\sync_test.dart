import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:generateur_facture/services/backup_service.dart';
import 'package:generateur_facture/models/product.dart';
import 'package:generateur_facture/models/category.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('Tests de Synchronisation et Compatibilité', () {
    test('Test de compatibilité des versions', () async {
      // Test avec une sauvegarde de version compatible
      final backupV1 = {
        'version': '1.0.0',
        'timestamp': DateTime.now().toIso8601String(),
        'data': {
          'categories': [
            {'id': 'cat1', 'name': 'Test Category', 'defaultPrice': 10.0},
          ],
          'products': [
            {
              'id': 'prod1',
              'name': 'Test Product',
              'price': 15.0,
              'quantity': 5,
              'description': 'Test description',
              'categoryId': 'cat1',
              'imageUrl': null,
            },
          ],
          'invoices': [],
          'tasks': [],
        },
      };

      // Test avec une sauvegarde de version incompatible
      final backupV2 = {
        'version': '2.0.0',
        'timestamp': DateTime.now().toIso8601String(),
        'data': {
          'categories': [
            {'id': 'cat1', 'name': 'Test Category V2', 'defaultPrice': 20.0},
          ],
          'products': [],
          'invoices': [],
          'tasks': [],
        },
      };

      // Test de restauration avec version compatible
      try {
        await BackupService.restoreFromBackup(backupV1);
        print('✅ Restauration version 1.0.0 réussie');
      } catch (e) {
        print('❌ Erreur version 1.0.0: $e');
      }

      // Test de restauration avec version incompatible (devrait utiliser la migration)
      try {
        await BackupService.restoreFromBackup(backupV2);
        print('✅ Migration version 2.0.0 réussie');
      } catch (e) {
        print('⚠️ Migration version 2.0.0: $e');
      }
    });

    test('Test de validation des données de sauvegarde', () async {
      // Test avec sauvegarde invalide (sans version)
      final invalidBackup = {
        'timestamp': DateTime.now().toIso8601String(),
        'data': {},
      };

      try {
        await BackupService.restoreFromBackup(invalidBackup);
        fail('Devrait lever une exception pour sauvegarde sans version');
      } catch (e) {
        expect(e.toString(), contains('Version de sauvegarde manquante'));
        print('✅ Validation version manquante: OK');
      }

      // Test avec données corrompues
      final corruptedBackup = {
        'version': '1.0.0',
        'timestamp': DateTime.now().toIso8601String(),
        'data': {
          'categories': 'invalid_data', // Devrait être une liste
        },
      };

      try {
        await BackupService.restoreFromBackup(corruptedBackup);
        print('⚠️ Restauration données corrompues: réussie (migration?)');
      } catch (e) {
        print('✅ Validation données corrompues: $e');
      }
    });

    test('Test de création de sauvegarde', () async {
      try {
        final backup = await BackupService.createBackup();

        expect(backup['version'], isNotNull);
        expect(backup['timestamp'], isNotNull);
        expect(backup['data'], isNotNull);

        final data = backup['data'] as Map<String, dynamic>;
        expect(data.containsKey('categories'), isTrue);
        expect(data.containsKey('products'), isTrue);
        expect(data.containsKey('invoices'), isTrue);
        expect(data.containsKey('tasks'), isTrue);

        print('✅ Création de sauvegarde: OK');
        print('   Version: ${backup['version']}');
        print('   Timestamp: ${backup['timestamp']}');
        print('   Catégories: ${(data['categories'] as List).length}');
        print('   Produits: ${(data['products'] as List).length}');
      } catch (e) {
        print('❌ Erreur création sauvegarde: $e');
      }
    });
  });
}
