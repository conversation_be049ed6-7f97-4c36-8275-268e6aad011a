import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/whatsapp_chat.dart';

class WhatsAppService {
  static final WhatsAppService _instance = WhatsAppService._internal();
  factory WhatsAppService() => _instance;
  WhatsAppService._internal();

  static const String _chatsKey = 'whatsapp_chats';
  static const String _messagesKey = 'whatsapp_messages';
  static const String _profilesKey = 'customer_profiles';

  List<WhatsAppChat> _chats = [];
  List<WhatsAppMessage> _allMessages = [];
  Map<String, CustomerProfile> _customerProfiles = {};

  /// Initialise le service WhatsApp
  Future<void> initialize() async {
    await _loadChats();
    await _loadMessages();
    await _loadCustomerProfiles();

    // Créer des données de démonstration si aucune n'existe
    if (_chats.isEmpty) {
      await _createDemoData();
    }
  }

  /// Crée des données de démonstration
  Future<void> _createDemoData() async {
    final demoChats = [
      WhatsAppChat(
        id: 'chat_1',
        customerName: '<PERSON>',
        customerPhone: '+33123456789',
        customerAvatar: null,
        status: ChatStatus.active,
        lastMessageTime: DateTime.now().subtract(const Duration(minutes: 5)),
        lastMessage: 'Bonjour, je cherche un iPhone 15',
        unreadCount: 2,
        messages: [],
        isAiEnabled: true,
      ),
      WhatsAppChat(
        id: 'chat_2',
        customerName: 'Marie Martin',
        customerPhone: '+33987654321',
        customerAvatar: null,
        status: ChatStatus.active,
        lastMessageTime: DateTime.now().subtract(const Duration(hours: 1)),
        lastMessage: 'Merci pour votre aide !',
        unreadCount: 0,
        messages: [],
        isAiEnabled: true,
      ),
      WhatsAppChat(
        id: 'chat_3',
        customerName: 'Pierre Durand',
        customerPhone: '+33555666777',
        customerAvatar: null,
        status: ChatStatus.pending,
        lastMessageTime: DateTime.now().subtract(const Duration(hours: 3)),
        lastMessage: 'J\'ai un problème avec ma commande',
        unreadCount: 1,
        messages: [],
        isAiEnabled: true,
      ),
    ];

    final demoMessages = [
      // Messages pour Jean Dupont
      WhatsAppMessage(
        id: 'msg_1',
        chatId: 'chat_1',
        content: 'Bonjour !',
        type: MessageType.text,
        status: MessageStatus.read,
        timestamp: DateTime.now().subtract(const Duration(minutes: 10)),
        isFromCustomer: true,
        detectedIntention: CustomerIntention.achat,
      ),
      WhatsAppMessage(
        id: 'msg_2',
        chatId: 'chat_1',
        content: 'Bonjour ! Comment puis-je vous aider aujourd\'hui ?',
        type: MessageType.text,
        status: MessageStatus.delivered,
        timestamp: DateTime.now().subtract(const Duration(minutes: 9)),
        isFromCustomer: false,
        isAiGenerated: true,
        aiModel: 'gpt-3.5-turbo',
      ),
      WhatsAppMessage(
        id: 'msg_3',
        chatId: 'chat_1',
        content: 'Je cherche un iPhone 15',
        type: MessageType.text,
        status: MessageStatus.read,
        timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
        isFromCustomer: true,
        detectedIntention: CustomerIntention.achat,
      ),

      // Messages pour Marie Martin
      WhatsAppMessage(
        id: 'msg_4',
        chatId: 'chat_2',
        content: 'Merci pour votre aide !',
        type: MessageType.text,
        status: MessageStatus.read,
        timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        isFromCustomer: true,
        detectedIntention: CustomerIntention.information,
      ),

      // Messages pour Pierre Durand
      WhatsAppMessage(
        id: 'msg_5',
        chatId: 'chat_3',
        content: 'J\'ai un problème avec ma commande',
        type: MessageType.text,
        status: MessageStatus.delivered,
        timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        isFromCustomer: true,
        detectedIntention: CustomerIntention.support,
      ),
    ];

    _chats = demoChats;
    _allMessages = demoMessages;

    // Mettre à jour les messages dans les chats
    for (final chat in _chats) {
      final chatMessages =
          _allMessages.where((m) => m.chatId == chat.id).toList();
      chatMessages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
      _chats[_chats.indexOf(chat)] = chat.copyWith(messages: chatMessages);
    }

    await _saveChats();
    await _saveMessages();
  }

  /// Obtient tous les chats
  List<WhatsAppChat> getChats() {
    return List.from(_chats);
  }

  /// Obtient un chat par ID
  WhatsAppChat? getChatById(String chatId) {
    try {
      return _chats.firstWhere((chat) => chat.id == chatId);
    } catch (e) {
      return null;
    }
  }

  /// Obtient les messages d'un chat
  List<WhatsAppMessage> getMessagesForChat(String chatId) {
    final messages = _allMessages.where((m) => m.chatId == chatId).toList();
    messages.sort((a, b) => a.timestamp.compareTo(b.timestamp));
    return messages;
  }

  /// Ajoute un nouveau message
  Future<void> addMessage(WhatsAppMessage message) async {
    _allMessages.add(message);

    // Mettre à jour le chat correspondant
    final chatIndex = _chats.indexWhere((chat) => chat.id == message.chatId);
    if (chatIndex != -1) {
      final chat = _chats[chatIndex];
      final updatedMessages = getMessagesForChat(message.chatId);

      _chats[chatIndex] = chat.copyWith(
        messages: updatedMessages,
        lastMessage: message.content,
        lastMessageTime: message.timestamp,
        unreadCount:
            message.isFromCustomer ? chat.unreadCount + 1 : chat.unreadCount,
      );
    }

    await _saveMessages();
    await _saveChats();
  }

  /// Marque les messages comme lus
  Future<void> markMessagesAsRead(String chatId) async {
    final chatIndex = _chats.indexWhere((chat) => chat.id == chatId);
    if (chatIndex != -1) {
      _chats[chatIndex] = _chats[chatIndex].copyWith(unreadCount: 0);
      await _saveChats();
    }

    // Marquer les messages comme lus
    for (int i = 0; i < _allMessages.length; i++) {
      if (_allMessages[i].chatId == chatId &&
          _allMessages[i].status != MessageStatus.read) {
        _allMessages[i] = _allMessages[i].copyWith(status: MessageStatus.read);
      }
    }
    await _saveMessages();
  }

  /// Crée un nouveau chat
  Future<WhatsAppChat> createChat({
    required String customerName,
    required String customerPhone,
    String? customerAvatar,
  }) async {
    final chatId = 'chat_${DateTime.now().millisecondsSinceEpoch}';

    final chat = WhatsAppChat(
      id: chatId,
      customerName: customerName,
      customerPhone: customerPhone,
      customerAvatar: customerAvatar,
      status: ChatStatus.active,
      lastMessageTime: DateTime.now(),
      lastMessage: '',
      unreadCount: 0,
      messages: [],
      isAiEnabled: true,
    );

    _chats.add(chat);
    await _saveChats();

    return chat;
  }

  /// Met à jour un chat
  Future<void> updateChat(WhatsAppChat updatedChat) async {
    final index = _chats.indexWhere((chat) => chat.id == updatedChat.id);
    if (index != -1) {
      _chats[index] = updatedChat;
      await _saveChats();
    }
  }

  /// Supprime un chat
  Future<void> deleteChat(String chatId) async {
    _chats.removeWhere((chat) => chat.id == chatId);
    _allMessages.removeWhere((message) => message.chatId == chatId);

    await _saveChats();
    await _saveMessages();
  }

  /// Archive un chat
  Future<void> archiveChat(String chatId) async {
    final chatIndex = _chats.indexWhere((chat) => chat.id == chatId);
    if (chatIndex != -1) {
      _chats[chatIndex] = _chats[chatIndex].copyWith(
        status: ChatStatus.archived,
      );
      await _saveChats();
    }
  }

  /// Obtient les chats non lus
  List<WhatsAppChat> getUnreadChats() {
    return _chats.where((chat) => chat.unreadCount > 0).toList();
  }

  /// Obtient le nombre total de messages non lus
  int getTotalUnreadCount() {
    return _chats.fold(0, (sum, chat) => sum + chat.unreadCount);
  }

  /// Recherche dans les chats
  List<WhatsAppChat> searchChats(String query) {
    if (query.isEmpty) return _chats;

    final lowercaseQuery = query.toLowerCase();
    return _chats.where((chat) {
      return chat.customerName.toLowerCase().contains(lowercaseQuery) ||
          chat.customerPhone.contains(query) ||
          chat.lastMessage.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  /// Obtient les statistiques des chats
  Map<String, dynamic> getChatStatistics() {
    final totalChats = _chats.length;
    final activeChats =
        _chats.where((c) => c.status == ChatStatus.active).length;
    final archivedChats =
        _chats.where((c) => c.status == ChatStatus.archived).length;
    final pendingChats =
        _chats.where((c) => c.status == ChatStatus.pending).length;
    final totalMessages = _allMessages.length;
    final aiMessages = _allMessages.where((m) => m.isAiGenerated).length;
    final customerMessages = _allMessages.where((m) => m.isFromCustomer).length;

    return {
      'totalChats': totalChats,
      'activeChats': activeChats,
      'archivedChats': archivedChats,
      'pendingChats': pendingChats,
      'totalMessages': totalMessages,
      'aiMessages': aiMessages,
      'customerMessages': customerMessages,
      'totalUnreadCount': getTotalUnreadCount(),
    };
  }

  // Méthodes de persistance
  Future<void> _loadChats() async {
    final prefs = await SharedPreferences.getInstance();
    final chatsJson = prefs.getString(_chatsKey);
    if (chatsJson != null) {
      final chatsList = jsonDecode(chatsJson) as List;
      _chats = chatsList.map((c) => WhatsAppChat.fromJson(c)).toList();
    }
  }

  Future<void> _saveChats() async {
    final prefs = await SharedPreferences.getInstance();
    final chatsJson = jsonEncode(_chats.map((c) => c.toJson()).toList());
    await prefs.setString(_chatsKey, chatsJson);
  }

  Future<void> _loadMessages() async {
    final prefs = await SharedPreferences.getInstance();
    final messagesJson = prefs.getString(_messagesKey);
    if (messagesJson != null) {
      final messagesList = jsonDecode(messagesJson) as List;
      _allMessages =
          messagesList.map((m) => WhatsAppMessage.fromJson(m)).toList();
    }
  }

  Future<void> _saveMessages() async {
    final prefs = await SharedPreferences.getInstance();
    final messagesJson = jsonEncode(
      _allMessages.map((m) => m.toJson()).toList(),
    );
    await prefs.setString(_messagesKey, messagesJson);
  }

  Future<void> _loadCustomerProfiles() async {
    final prefs = await SharedPreferences.getInstance();
    final profilesJson = prefs.getString(_profilesKey);
    if (profilesJson != null) {
      final profilesMap = jsonDecode(profilesJson) as Map<String, dynamic>;
      _customerProfiles = profilesMap.map(
        (key, value) => MapEntry(key, CustomerProfile.fromJson(value)),
      );
    }
  }

  Future<void> _saveCustomerProfiles() async {
    final prefs = await SharedPreferences.getInstance();
    final profilesJson = jsonEncode(
      _customerProfiles.map((key, value) => MapEntry(key, value.toJson())),
    );
    await prefs.setString(_profilesKey, profilesJson);
  }

  /// Obtient ou crée un profil client
  CustomerProfile getOrCreateCustomerProfile(String phone, String name) {
    if (_customerProfiles.containsKey(phone)) {
      return _customerProfiles[phone]!;
    }

    final profile = CustomerProfile(
      id: 'profile_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      phone: phone,
      preferences: [],
      purchaseHistory: [],
      behaviorData: {},
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _customerProfiles[phone] = profile;
    _saveCustomerProfiles();

    return profile;
  }

  /// Met à jour un profil client
  Future<void> updateCustomerProfile(CustomerProfile profile) async {
    _customerProfiles[profile.phone] = profile;
    await _saveCustomerProfiles();
  }

  /// Met à jour le statut d'un message
  Future<void> updateMessageStatus(
    String messageId,
    MessageStatus status,
  ) async {
    final messageIndex = _allMessages.indexWhere((m) => m.id == messageId);
    if (messageIndex != -1) {
      final message = _allMessages[messageIndex];
      _allMessages[messageIndex] = message.copyWith(status: status);
      await _saveMessages();
    }
  }
}
