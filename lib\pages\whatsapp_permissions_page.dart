import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/permission_service.dart';

class WhatsAppPermissionsPage extends StatefulWidget {
  const WhatsAppPermissionsPage({super.key});

  @override
  State<WhatsAppPermissionsPage> createState() => _WhatsAppPermissionsPageState();
}

class _WhatsAppPermissionsPageState extends State<WhatsAppPermissionsPage> {
  final PermissionService _permissionService = PermissionService();
  Map<String, PermissionStatus> _permissionStatuses = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  Future<void> _checkPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final statuses = await _permissionService.checkAllPermissions();
      setState(() {
        _permissionStatuses = statuses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la vérification: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _requestAllPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final results = await _permissionService.requestAllWhatsAppPermissions();
      
      // Afficher les résultats
      final granted = results.values.where((granted) => granted).length;
      final total = results.length;
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$granted/$total permissions accordées'),
            backgroundColor: granted == total ? Colors.green : Colors.orange,
          ),
        );
      }
      
      // Recharger les statuts
      await _checkPermissions();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la demande: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _requestSinglePermission(String permission) async {
    bool granted = false;
    
    switch (permission) {
      case 'camera':
        granted = await _permissionService.requestCameraPermission();
        break;
      case 'microphone':
        granted = await _permissionService.requestMicrophonePermission();
        break;
      case 'storage':
        granted = await _permissionService.requestStoragePermission();
        break;
      case 'photos':
        granted = await _permissionService.requestPhotosPermission();
        break;
      case 'videos':
        granted = await _permissionService.requestVideosPermission();
        break;
      case 'audio':
        granted = await _permissionService.requestAudioPermission();
        break;
    }
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            granted 
              ? 'Permission $permission accordée' 
              : 'Permission $permission refusée'
          ),
          backgroundColor: granted ? Colors.green : Colors.red,
        ),
      );
    }
    
    // Recharger les statuts
    await _checkPermissions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Permissions WhatsApp'),
        backgroundColor: const Color(0xFF25D366),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _checkPermissions,
            tooltip: 'Actualiser',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(),
                  const SizedBox(height: 24),
                  _buildPermissionsList(),
                  const SizedBox(height: 24),
                  _buildActionButtons(),
                ],
              ),
            ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security,
                color: Colors.green.shade700,
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Permissions nécessaires',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Ces permissions sont nécessaires pour envoyer des médias et documents via WhatsApp.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.green.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'État des permissions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ..._permissionStatuses.entries.map((entry) {
          return _buildPermissionTile(entry.key, entry.value);
        }),
      ],
    );
  }

  Widget _buildPermissionTile(String permission, PermissionStatus status) {
    final isGranted = status.isGranted;
    final isDenied = status.isDenied;
    final isPermanentlyDenied = status.isPermanentlyDenied;
    
    Color statusColor = Colors.grey;
    IconData statusIcon = Icons.help_outline;
    String statusText = 'Inconnu';
    
    if (isGranted) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
      statusText = 'Accordée';
    } else if (isPermanentlyDenied) {
      statusColor = Colors.red;
      statusIcon = Icons.block;
      statusText = 'Refusée définitivement';
    } else if (isDenied) {
      statusColor = Colors.orange;
      statusIcon = Icons.warning;
      statusText = 'Refusée';
    }
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          _getPermissionIcon(permission),
          color: statusColor,
          size: 28,
        ),
        title: Text(
          _getPermissionTitle(permission),
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Text(_getPermissionDescription(permission)),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                // ignore: deprecated_member_use
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                // ignore: deprecated_member_use
                border: Border.all(color: statusColor.withOpacity(0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(statusIcon, size: 16, color: statusColor),
                  const SizedBox(width: 4),
                  Text(
                    statusText,
                    style: TextStyle(
                      fontSize: 12,
                      color: statusColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            if (!isGranted) ...[
              const SizedBox(width: 8),
              IconButton(
                icon: const Icon(Icons.settings),
                onPressed: isPermanentlyDenied
                    ? () => openAppSettings()
                    : () => _requestSinglePermission(permission),
                tooltip: isPermanentlyDenied 
                    ? 'Ouvrir les paramètres' 
                    : 'Demander la permission',
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    final allGranted = _permissionStatuses.values.every((status) => status.isGranted);
    
    return Column(
      children: [
        if (!allGranted) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _requestAllPermissions,
              icon: const Icon(Icons.security),
              label: const Text('Demander toutes les permissions'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF25D366),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => openAppSettings(),
            icon: const Icon(Icons.settings),
            label: const Text('Ouvrir les paramètres'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
      ],
    );
  }

  IconData _getPermissionIcon(String permission) {
    switch (permission.toLowerCase()) {
      case 'camera':
        return Icons.camera_alt;
      case 'microphone':
        return Icons.mic;
      case 'storage':
        return Icons.storage;
      case 'photos':
        return Icons.photo_library;
      case 'videos':
        return Icons.videocam;
      case 'audio':
        return Icons.audiotrack;
      default:
        return Icons.security;
    }
  }

  String _getPermissionTitle(String permission) {
    switch (permission.toLowerCase()) {
      case 'camera':
        return 'Caméra';
      case 'microphone':
        return 'Microphone';
      case 'storage':
        return 'Stockage';
      case 'photos':
        return 'Photos';
      case 'videos':
        return 'Vidéos';
      case 'audio':
        return 'Audio';
      default:
        return permission;
    }
  }

  String _getPermissionDescription(String permission) {
    switch (permission.toLowerCase()) {
      case 'camera':
        return 'Prendre des photos et vidéos';
      case 'microphone':
        return 'Enregistrer des messages vocaux';
      case 'storage':
        return 'Accéder aux fichiers';
      case 'photos':
        return 'Accéder aux photos';
      case 'videos':
        return 'Accéder aux vidéos';
      case 'audio':
        return 'Accéder aux fichiers audio';
      default:
        return 'Permission système';
    }
  }
}
