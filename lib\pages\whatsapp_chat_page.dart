import 'dart:io';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/whatsapp_chat.dart';
import '../services/whatsapp_service.dart';
import '../services/ai_service.dart';
import '../services/media_service.dart';
import '../services/permission_service.dart';
import 'whatsapp_permissions_page.dart';

class WhatsAppChatPage extends StatefulWidget {
  final WhatsAppChat chat;

  const WhatsAppChatPage({super.key, required this.chat});

  @override
  State<WhatsAppChatPage> createState() => _WhatsAppChatPageState();
}

class _WhatsAppChatPageState extends State<WhatsAppChatPage>
    with TickerProviderStateMixin {
  final WhatsAppService _whatsappService = WhatsAppService();
  final AIService _aiService = AIService();
  final MediaService _mediaService = MediaService();
  final PermissionService _permissionService = PermissionService();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<WhatsAppMessage> _messages = [];
  bool _isLoading = true;
  bool _isSendingMessage = false;
  bool _isAiTyping = false;

  // Contrôleurs d'animation
  late AnimationController _fadeController;
  late AnimationController _typingController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _typingAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadMessages();
    _markMessagesAsRead();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _typingController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _typingAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _typingController, curve: Curves.easeInOut),
    );

    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _typingController.dispose();
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadMessages() async {
    setState(() => _isLoading = true);

    try {
      final messages = _whatsappService.getMessagesForChat(widget.chat.id);
      setState(() {
        _messages = messages;
        _isLoading = false;
      });

      // Faire défiler vers le bas
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _markMessagesAsRead() async {
    await _whatsappService.markMessagesAsRead(widget.chat.id);
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  Future<void> _sendMessage() async {
    final messageText = _messageController.text.trim();
    if (messageText.isEmpty || _isSendingMessage) return;

    setState(() {
      _isSendingMessage = true;
    });

    try {
      // Créer le message utilisateur
      final userMessage = WhatsAppMessage(
        id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
        chatId: widget.chat.id,
        content: messageText,
        type: MessageType.text,
        status: MessageStatus.sent,
        timestamp: DateTime.now(),
        isFromCustomer: false, // Message de l'agent
      );

      // Ajouter le message à la liste locale
      setState(() {
        _messages.add(userMessage);
      });

      // Sauvegarder le message
      await _whatsappService.addMessage(userMessage);

      // Vider le champ de texte
      _messageController.clear();

      // Faire défiler vers le bas
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });

      // Si l'IA est activée, générer une réponse automatique
      if (widget.chat.isAiEnabled) {
        await _generateAIResponse(messageText);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'envoi: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSendingMessage = false;
      });
    }
  }

  Future<void> _generateAIResponse(String userMessage) async {
    setState(() {
      _isAiTyping = true;
    });
    _typingController.repeat();

    try {
      // Simuler un délai de frappe
      await Future.delayed(const Duration(seconds: 2));

      // Générer la réponse IA
      final aiResponse = await _aiService.generateResponse(
        message: userMessage,
        context: 'Conversation WhatsApp avec ${widget.chat.customerName}',
        conversationHistory: _messages.take(10).toList(),
      );

      if (aiResponse != null && mounted) {
        // Créer le message IA
        final aiMessage = WhatsAppMessage(
          id: 'msg_${DateTime.now().millisecondsSinceEpoch}_ai',
          chatId: widget.chat.id,
          content: aiResponse.content,
          type: MessageType.text,
          status: MessageStatus.delivered,
          timestamp: DateTime.now(),
          isFromCustomer: false,
          isAiGenerated: true,
          aiModel: aiResponse.model.modelName,
        );

        // Ajouter le message à la liste locale
        setState(() {
          _messages.add(aiMessage);
        });

        // Sauvegarder le message
        await _whatsappService.addMessage(aiMessage);

        // Faire défiler vers le bas
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }
    } catch (e) {
      debugPrint('Erreur lors de la génération de réponse IA: $e');
    } finally {
      setState(() {
        _isAiTyping = false;
      });
      _typingController.stop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF25D366),
        foregroundColor: Colors.white,
        title: Row(
          children: [
            CircleAvatar(
              backgroundColor: Colors.white,
              backgroundImage:
                  widget.chat.customerAvatar != null
                      ? NetworkImage(widget.chat.customerAvatar!)
                      : null,
              child:
                  widget.chat.customerAvatar == null
                      ? Text(
                        widget.chat.customerName.isNotEmpty
                            ? widget.chat.customerName[0].toUpperCase()
                            : '?',
                        style: const TextStyle(
                          color: Color(0xFF25D366),
                          fontWeight: FontWeight.bold,
                        ),
                      )
                      : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.chat.customerName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    widget.chat.customerPhone,
                    style: const TextStyle(fontSize: 12, color: Colors.white70),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          if (widget.chat.isAiEnabled)
            Container(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.smart_toy, size: 16, color: Colors.white),
                  const SizedBox(width: 4),
                  const Text(
                    'IA',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: _showChatOptions,
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(child: _buildMessagesList()),
          if (_isAiTyping) _buildTypingIndicator(),
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildMessagesList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Aucun message',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Commencez la conversation',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/whatsapp_bg.png'),
            fit: BoxFit.cover,
            opacity: 0.1,
          ),
        ),
        child: ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          itemCount: _messages.length,
          itemBuilder: (context, index) {
            final message = _messages[index];
            return _buildMessageBubble(message);
          },
        ),
      ),
    );
  }

  Widget _buildMessageBubble(WhatsAppMessage message) {
    final isFromMe = !message.isFromCustomer;
    final timeFormat = DateFormat('HH:mm');

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment:
            isFromMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!isFromMe) const SizedBox(width: 48),
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              decoration: BoxDecoration(
                color: isFromMe ? const Color(0xFFDCF8C6) : Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: Radius.circular(isFromMe ? 16 : 4),
                  bottomRight: Radius.circular(isFromMe ? 4 : 16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (message.isAiGenerated)
                    Container(
                      margin: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.smart_toy,
                            size: 12,
                            color: Colors.blue[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'IA',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.blue[600],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  _buildMessageContent(message),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        timeFormat.format(message.timestamp),
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      if (isFromMe) ...[
                        const SizedBox(width: 4),
                        Icon(
                          message.status == MessageStatus.read
                              ? Icons.done_all
                              : message.status == MessageStatus.delivered
                              ? Icons.done_all
                              : Icons.done,
                          size: 16,
                          color:
                              message.status == MessageStatus.read
                                  ? Colors.blue
                                  : Colors.grey[600],
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (isFromMe) const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: const Color(0xFF25D366),
            child: Icon(Icons.smart_toy, size: 16, color: Colors.white),
          ),
          const SizedBox(width: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: AnimatedBuilder(
              animation: _typingAnimation,
              builder: (context, child) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildTypingDot(0),
                    const SizedBox(width: 4),
                    _buildTypingDot(1),
                    const SizedBox(width: 4),
                    _buildTypingDot(2),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingDot(int index) {
    final delay = index * 0.2;
    final animation = Tween<double>(begin: 0.4, end: 1.0).animate(
      CurvedAnimation(
        parent: _typingController,
        curve: Interval(delay, delay + 0.4, curve: Curves.easeInOut),
      ),
    );

    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Opacity(
          opacity: animation.value,
          child: Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: Colors.grey[600],
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: _showAttachmentOptions,
            icon: const Icon(Icons.attach_file),
            color: Colors.grey[600],
          ),
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Tapez votre message...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            decoration: const BoxDecoration(
              color: Color(0xFF25D366),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _isSendingMessage ? null : _sendMessage,
              icon:
                  _isSendingMessage
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : const Icon(Icons.send, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _showChatOptions() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(
                  widget.chat.isAiEnabled
                      ? Icons.smart_toy_outlined
                      : Icons.smart_toy,
                  color: widget.chat.isAiEnabled ? Colors.blue : Colors.grey,
                ),
                title: Text(
                  widget.chat.isAiEnabled
                      ? 'Désactiver l\'IA'
                      : 'Activer l\'IA',
                ),
                onTap: () {
                  Navigator.pop(context);
                  _toggleAI();
                },
              ),
              ListTile(
                leading: const Icon(Icons.person),
                title: const Text('Profil client'),
                onTap: () {
                  Navigator.pop(context);
                  // Afficher le profil client
                },
              ),
              ListTile(
                leading: const Icon(Icons.history),
                title: const Text('Historique'),
                onTap: () {
                  Navigator.pop(context);
                  // Afficher l'historique
                },
              ),
            ],
          ),
    );
  }

  void _toggleAI() async {
    final updatedChat = widget.chat.copyWith(
      isAiEnabled: !widget.chat.isAiEnabled,
    );

    await _whatsappService.updateChat(updatedChat);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            updatedChat.isAiEnabled
                ? 'IA activée pour cette conversation'
                : 'IA désactivée pour cette conversation',
          ),
          backgroundColor:
              updatedChat.isAiEnabled ? Colors.green : Colors.orange,
        ),
      );
    }
  }

  Widget _buildMessageContent(WhatsAppMessage message) {
    switch (message.type) {
      case MessageType.text:
        return Text(message.content, style: const TextStyle(fontSize: 16));

      case MessageType.image:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (message.mediaUrl != null) ...[
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.file(
                  File(message.mediaUrl!),
                  width: 200,
                  height: 200,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.broken_image,
                            size: 48,
                            color: Colors.grey,
                          ),
                          Text(
                            'Image non disponible',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
              if (message.mediaCaption != null &&
                  message.mediaCaption!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  message.mediaCaption!,
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ] else ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.image, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text(message.content),
                  ],
                ),
              ),
            ],
          ],
        );

      case MessageType.video:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 200,
              height: 150,
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  if (message.mediaUrl != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        width: 200,
                        height: 150,
                        color: Colors.black,
                        child: const Icon(
                          Icons.play_circle_fill,
                          size: 48,
                          color: Colors.white,
                        ),
                      ),
                    )
                  else
                    const Icon(Icons.videocam, size: 48, color: Colors.white),
                ],
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.videocam, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  message.fileName ?? 'Vidéo',
                  style: const TextStyle(fontSize: 14),
                ),
                if (message.fileSize != null) ...[
                  const SizedBox(width: 8),
                  Text(
                    _mediaService.formatFileSize(message.fileSize!),
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ],
            ),
          ],
        );

      case MessageType.audio:
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: const BoxDecoration(
                  color: Color(0xFF25D366),
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.play_arrow, color: Colors.white),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.fileName ?? 'Audio',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (message.fileSize != null)
                    Text(
                      _mediaService.formatFileSize(message.fileSize!),
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                ],
              ),
            ],
          ),
        );

      case MessageType.document:
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _mediaService.getFileIcon(message.mimeType ?? ''),
                  color: Colors.blue[700],
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      message.fileName ?? 'Document',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (message.fileSize != null)
                      Text(
                        _mediaService.formatFileSize(message.fileSize!),
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                  ],
                ),
              ),
              Icon(Icons.download, color: Colors.grey[600], size: 20),
            ],
          ),
        );

      default:
        return Text(message.content, style: const TextStyle(fontSize: 16));
    }
  }

  void _showAttachmentOptions() async {
    // Vérifier les permissions avant d'afficher les options
    final permissions = await _permissionService.checkAllPermissions();
    final hasBasicPermissions = permissions.values.any(
      (status) => status.isGranted,
    );

    if (!hasBasicPermissions) {
      // Afficher un dialogue pour demander les permissions
      final shouldRequest = await _showPermissionRequiredDialog();
      if (shouldRequest == true && mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const WhatsAppPermissionsPage(),
          ),
        );
      }
      return;
    }

    if (!mounted) return;

    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Envoyer un fichier',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildAttachmentOption(
                      icon: Icons.photo_camera,
                      label: 'Caméra',
                      color: Colors.blue,
                      onTap: () {
                        Navigator.pop(context);
                        _pickImage(ImageSource.camera);
                      },
                    ),
                    _buildAttachmentOption(
                      icon: Icons.photo_library,
                      label: 'Galerie',
                      color: Colors.green,
                      onTap: () {
                        Navigator.pop(context);
                        _pickImage(ImageSource.gallery);
                      },
                    ),
                    _buildAttachmentOption(
                      icon: Icons.videocam,
                      label: 'Vidéo',
                      color: Colors.red,
                      onTap: () {
                        Navigator.pop(context);
                        _pickVideo();
                      },
                    ),
                    _buildAttachmentOption(
                      icon: Icons.insert_drive_file,
                      label: 'Document',
                      color: Colors.orange,
                      onTap: () {
                        Navigator.pop(context);
                        _pickDocument();
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildAttachmentOption(
                      icon: Icons.audiotrack,
                      label: 'Audio',
                      color: Colors.purple,
                      onTap: () {
                        Navigator.pop(context);
                        _pickAudio();
                      },
                    ),
                    _buildAttachmentOption(
                      icon: Icons.mic,
                      label: 'Enregistrer',
                      color: Colors.teal,
                      onTap: () {
                        Navigator.pop(context);
                        _recordAudio();
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Icon(icon, color: color, size: 30),
          ),
          const SizedBox(height: 8),
          Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[700])),
        ],
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final mediaFile = await _mediaService.pickImage(source: source);
      if (mediaFile != null) {
        await _sendMediaMessage(mediaFile);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sélection d\'image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickVideo() async {
    try {
      final mediaFile = await _mediaService.pickVideo();
      if (mediaFile != null) {
        await _sendMediaMessage(mediaFile);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sélection de vidéo: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickDocument() async {
    try {
      final mediaFile = await _mediaService.pickDocument();
      if (mediaFile != null) {
        await _sendMediaMessage(mediaFile);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sélection de document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickAudio() async {
    try {
      final mediaFile = await _mediaService.pickAudio();
      if (mediaFile != null) {
        await _sendMediaMessage(mediaFile);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la sélection d\'audio: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _recordAudio() async {
    try {
      final mediaFile = await _mediaService.recordAudio();
      if (mediaFile != null) {
        await _sendMediaMessage(mediaFile);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'enregistrement: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _sendMediaMessage(MediaFile mediaFile) async {
    setState(() {
      _isSendingMessage = true;
    });

    try {
      // Sauvegarder le fichier média
      final savedPath = await _mediaService.saveMediaFile(mediaFile);
      if (savedPath == null) {
        throw Exception('Impossible de sauvegarder le fichier');
      }

      // Créer le message avec média
      final message = WhatsAppMessage(
        id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
        chatId: widget.chat.id,
        content: _getMediaContent(mediaFile),
        type: _getMessageType(mediaFile.type),
        status: MessageStatus.sent,
        timestamp: DateTime.now(),
        isFromCustomer: false,
        mediaUrl: savedPath,
        fileName: mediaFile.name,
        fileSize: mediaFile.size,
        mimeType: mediaFile.mimeType,
      );

      // Ajouter le message à la liste locale
      setState(() {
        _messages.add(message);
      });

      // Sauvegarder le message
      await _whatsappService.addMessage(message);

      // Faire défiler vers le bas
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${_getMediaTypeName(mediaFile.type)} envoyé(e)'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'envoi: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSendingMessage = false;
      });
    }
  }

  String _getMediaContent(MediaFile mediaFile) {
    switch (mediaFile.type) {
      case MediaType.image:
        return '[Image]';
      case MediaType.video:
        return '[Vidéo]';
      case MediaType.audio:
        return '[Audio]';
      case MediaType.document:
        return '[Document: ${mediaFile.name}]';
    }
  }

  MessageType _getMessageType(MediaType mediaType) {
    switch (mediaType) {
      case MediaType.image:
        return MessageType.image;
      case MediaType.video:
        return MessageType.video;
      case MediaType.audio:
        return MessageType.audio;
      case MediaType.document:
        return MessageType.document;
    }
  }

  String _getMediaTypeName(MediaType mediaType) {
    switch (mediaType) {
      case MediaType.image:
        return 'Image';
      case MediaType.video:
        return 'Vidéo';
      case MediaType.audio:
        return 'Audio';
      case MediaType.document:
        return 'Document';
    }
  }

  Future<bool?> _showPermissionRequiredDialog() async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.security, color: Colors.orange, size: 28),
                const SizedBox(width: 12),
                const Expanded(child: Text('Permissions requises')),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Pour envoyer des médias et documents via WhatsApp, vous devez accorder les permissions suivantes :',
                ),
                const SizedBox(height: 16),
                _buildPermissionItem(
                  Icons.camera_alt,
                  'Caméra',
                  'Prendre des photos et vidéos',
                ),
                _buildPermissionItem(
                  Icons.photo_library,
                  'Photos',
                  'Accéder à la galerie',
                ),
                _buildPermissionItem(
                  Icons.storage,
                  'Stockage',
                  'Accéder aux fichiers',
                ),
                _buildPermissionItem(
                  Icons.mic,
                  'Microphone',
                  'Enregistrer des messages vocaux',
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF25D366),
                  foregroundColor: Colors.white,
                ),
                child: const Text('Gérer les permissions'),
              ),
            ],
          ),
    );
  }

  Widget _buildPermissionItem(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
