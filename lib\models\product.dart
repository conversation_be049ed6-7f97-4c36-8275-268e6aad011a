class Product {
  final String id;
  String name;
  double price;
  int quantity;
  String description;
  String categoryId; // Lié à un modèle Category
  String? imageUrl;

  Product({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    this.description = '',
    required this.categoryId,
    this.imageUrl,
  });

  // Méthode pour convertir un Product en Map (pour la sérialisation JSON par exemple)
  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'price': price,
        'quantity': quantity,
        'description': description,
        'categoryId': categoryId,
        'imageUrl': imageUrl,
      };

  // Méthode factory pour créer un Product à partir d'un Map
  factory Product.fromJson(Map<String, dynamic> json) => Product(
        id: json['id'] as String,
        name: json['name'] as String,
        price: (json['price'] as num).toDouble(),
        quantity: json['quantity'] as int,
        description: json['description'] as String? ?? '',
        categoryId: json['categoryId'] as String,
        imageUrl: json['imageUrl'] as String?,
      );

  Product copyWith({
    String? id,
    String? name,
    double? price,
    int? quantity,
    String? description,
    String? categoryId,
    String? imageUrl,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      description: description ?? this.description,
      categoryId: categoryId ?? this.categoryId,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }
}