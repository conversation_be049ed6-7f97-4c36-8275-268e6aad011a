import 'package:flutter/material.dart';
import '../services/ai_service.dart';
import '../models/ai_models.dart';

class GeminiFlashTestPage extends StatefulWidget {
  const GeminiFlashTestPage({super.key});

  @override
  State<GeminiFlashTestPage> createState() => _GeminiFlashTestPageState();
}

class _GeminiFlashTestPageState extends State<GeminiFlashTestPage> {
  final AIService _aiService = AIService();
  final TextEditingController _promptController = TextEditingController();
  
  bool _isLoading = false;
  String _result15Flash = '';
  String _result25Flash = '';
  DateTime? _startTime;
  Duration? _duration15Flash;
  Duration? _duration25Flash;

  @override
  void initState() {
    super.initState();
    _promptController.text = 'Écris un court poème sur l\'intelligence artificielle en français.';
  }

  @override
  void dispose() {
    _promptController.dispose();
    super.dispose();
  }

  Future<void> _testBothModels() async {
    if (_promptController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez entrer un prompt'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _result15Flash = '';
      _result25Flash = '';
      _duration15Flash = null;
      _duration25Flash = null;
    });

    final prompt = _promptController.text.trim();

    try {
      // Test Gemini 1.5 Flash
      _startTime = DateTime.now();
      final response15 = await _aiService.generateResponse(
        message: prompt,
        context: 'Test Gemini 1.5 Flash',
        preferredModel: AIModel.gemini15Flash,
      );
      _duration15Flash = DateTime.now().difference(_startTime!);
      
      setState(() {
        _result15Flash = response15?.content ?? 'Aucune réponse reçue';
      });

      // Test Gemini 2.5 Flash
      _startTime = DateTime.now();
      final response25 = await _aiService.generateResponse(
        message: prompt,
        context: 'Test Gemini 2.5 Flash',
        preferredModel: AIModel.gemini25Flash,
      );
      _duration25Flash = DateTime.now().difference(_startTime!);
      
      setState(() {
        _result25Flash = response25?.content ?? 'Aucune réponse reçue';
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _result15Flash = 'Erreur: $e';
        _result25Flash = 'Erreur: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Gemini Flash'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildInfoCard(),
            const SizedBox(height: 16),
            _buildPromptInput(),
            const SizedBox(height: 16),
            _buildTestButton(),
            const SizedBox(height: 24),
            if (_isLoading) _buildLoadingIndicator(),
            if (!_isLoading && (_result15Flash.isNotEmpty || _result25Flash.isNotEmpty))
              _buildResults(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: Colors.blue,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  'Test des modèles Gemini Flash',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Cette page permet de tester et comparer les performances des modèles Gemini 1.5 Flash et 2.5 Flash.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue.shade700,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Assurez-vous d\'avoir configuré votre clé API Gemini dans les paramètres IA.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPromptInput() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Prompt de test',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _promptController,
              maxLines: 4,
              decoration: const InputDecoration(
                hintText: 'Entrez votre prompt ici...',
                border: OutlineInputBorder(),
                filled: true,
                fillColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestButton() {
    return ElevatedButton.icon(
      onPressed: _isLoading ? null : _testBothModels,
      icon: _isLoading 
        ? const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          )
        : const Icon(Icons.play_arrow),
      label: Text(_isLoading ? 'Test en cours...' : 'Tester les deux modèles'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        textStyle: const TextStyle(fontSize: 16),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(24),
        child: Column(
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Test des modèles en cours...',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResults() {
    return Column(
      children: [
        _buildResultCard(
          title: 'Gemini 1.5 Flash',
          result: _result15Flash,
          duration: _duration15Flash,
          color: Colors.green,
        ),
        const SizedBox(height: 16),
        _buildResultCard(
          title: 'Gemini 2.5 Flash',
          result: _result25Flash,
          duration: _duration25Flash,
          color: Colors.purple,
        ),
        const SizedBox(height: 16),
        if (_duration15Flash != null && _duration25Flash != null)
          _buildComparisonCard(),
      ],
    );
  }

  Widget _buildResultCard({
    required String title,
    required String result,
    Duration? duration,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  color: color,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const Spacer(),
                if (duration != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      // ignore: deprecated_member_use
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      // ignore: deprecated_member_use
                      border: Border.all(color: color.withOpacity(0.3)),
                    ),
                    child: Text(
                      '${duration.inMilliseconds}ms',
                      style: TextStyle(
                        fontSize: 12,
                        color: color,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                result,
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonCard() {
    final faster = _duration15Flash!.inMilliseconds < _duration25Flash!.inMilliseconds 
        ? 'Gemini 1.5 Flash' 
        : 'Gemini 2.5 Flash';
    final difference = (_duration15Flash!.inMilliseconds - _duration25Flash!.inMilliseconds).abs();
    
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.compare_arrows,
                  color: Colors.blue.shade700,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Comparaison',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '🏆 $faster est plus rapide de ${difference}ms',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Gemini 1.5 Flash: ${_duration15Flash!.inMilliseconds}ms',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              'Gemini 2.5 Flash: ${_duration25Flash!.inMilliseconds}ms',
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}
