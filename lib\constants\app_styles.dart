import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Constantes de styles pour assurer la cohérence dans toute l'application
class AppStyles {
  // Styles d'AppBar
  static AppBarTheme get appBarTheme => AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        titleTextStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );

  // Styles de FloatingActionButton
  static FloatingActionButtonThemeData get fabTheme =>
      FloatingActionButtonThemeData(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      );

  // Styles de cartes de statistiques
  static TextStyle get statCardValue => const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w800,
        color: Colors.white,
        height: 1.0,
      );

  static TextStyle get statCardTitle => TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: AppColors.cardTextSecondary,
        height: 1.0,
      );

  // Styles de titres de section
  static TextStyle get sectionHeader => TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w700,
        color: AppColors.onSurface,
        letterSpacing: 0.5,
      );

  // Styles de cartes
  static BoxDecoration get cardDecoration => BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(18),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      );

  // Styles de boutons
  static ButtonStyle get primaryButton => ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      );

  static ButtonStyle get secondaryButton => ElevatedButton.styleFrom(
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.onSurface,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: AppColors.onSurfaceSecondary),
        ),
      );

  // Styles de champs de texte
  static InputDecoration get textFieldDecoration => InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary),
        ),
        filled: true,
        fillColor: AppColors.surface,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      );

  // Styles de statut
  static TextStyle statusTextStyle(Color color) => TextStyle(
        color: color,
        fontSize: 12,
        fontWeight: FontWeight.w500,
      );

  static BoxDecoration statusChipDecoration(Color color) => BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      );
}
