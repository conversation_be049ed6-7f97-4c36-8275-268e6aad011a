import 'package:flutter/material.dart';

/// Constantes de couleurs pour assurer la cohérence dans toute l'application
class AppColors {
  // Couleurs principales
  static Color get primary => Colors.blue[900]!;
  static Color get primaryLight => Colors.blue[700]!;
  static Color get primaryDark => Colors.blue[800]!;

  // Couleurs de statut (cohérentes avec le dashboard)
  static Color get success => Colors.green;
  static Color get warning => Colors.orange;
  static Color get error => Colors.red;
  static Color get info => Colors.blue;
  static Color get purple => Colors.purple;

  // Couleurs spécifiques aux fonctionnalités
  static Color get invoiceTotal => Colors.blue;
  static Color get invoicePending => Colors.orange;
  static Color get invoiceDelivered => Colors.green;
  static Color get invoiceRevenue => Colors.purple;

  static Color get inventoryTotal => Colors.blue;
  static Color get inventoryLowStock => Colors.orange;
  static Color get inventoryOutOfStock => Colors.red;
  static Color get inventoryValue => Colors.green;

  // Couleurs d'interface
  static Color get background => Colors.grey[50]!;
  static Color get surface => Colors.white;
  static Color get onSurface => Colors.grey[800]!;
  static Color get onSurfaceSecondary => Colors.grey[600]!;

  // Couleurs pour les cartes de statistiques
  static Color get cardIconColor => Colors.white;
  static Color get cardTextColor => Colors.white;
  static Color get cardTextSecondary => Colors.white.withValues(alpha: 0.9);

  // Couleurs d'état des produits
  static Color getStockStatusColor(int quantity) {
    if (quantity == 0) return error;
    if (quantity <= 5) return warning;
    return success;
  }

  // Couleurs d'état des factures
  static Color getInvoiceStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'payee':
      case 'livrée':
      case 'delivered':
        return success;
      case 'en_attente':
      case 'pending':
        return warning;
      case 'annulee':
      case 'cancelled':
        return error;
      default:
        return info;
    }
  }
}

/// Extension pour faciliter l'utilisation des couleurs avec transparence
extension ColorExtension on Color {
  Color withAlpha(double alpha) => withValues(alpha: alpha);
}
