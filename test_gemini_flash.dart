import 'package:flutter/material.dart';
import 'lib/services/ai_service.dart';
import 'lib/models/ai_models.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('=== Test des configurations Gemini Flash ===');
  
  final aiService = AIService();
  await aiService.initialize();
  
  final configurations = aiService.configurations;
  
  print('Nombre total de configurations: ${configurations.length}');
  print('');
  
  // Vérifier les modèles Gemini
  final geminiConfigs = configurations.where(
    (config) => config.provider == AIProvider.gemini,
  ).toList();
  
  print('Configurations Gemini trouvées: ${geminiConfigs.length}');
  
  for (final config in geminiConfigs) {
    print('- ${config.model.modelName}');
    print('  Provider: ${config.provider.displayName}');
    print('  Temperature: ${config.temperature}');
    print('  Max Tokens: ${config.maxTokens}');
    print('  Enabled: ${config.isEnabled}');
    print('  API Key configured: ${config.apiKey.isNotEmpty}');
    print('');
  }
  
  // Vérifier spécifiquement les modèles Flash
  final flash15 = configurations.where(
    (config) => config.model == AIModel.gemini15Flash,
  ).toList();
  
  final flash25 = configurations.where(
    (config) => config.model == AIModel.gemini25Flash,
  ).toList();
  
  print('=== Vérification des modèles Flash ===');
  print('Gemini 1.5 Flash trouvé: ${flash15.isNotEmpty}');
  if (flash15.isNotEmpty) {
    print('  Configuration: ${flash15.first.model.modelName}');
    print('  Temperature: ${flash15.first.temperature}');
    print('  Max Tokens: ${flash15.first.maxTokens}');
  }
  
  print('Gemini 2.5 Flash trouvé: ${flash25.isNotEmpty}');
  if (flash25.isNotEmpty) {
    print('  Configuration: ${flash25.first.model.modelName}');
    print('  Temperature: ${flash25.first.temperature}');
    print('  Max Tokens: ${flash25.first.maxTokens}');
  }
  
  print('');
  print('=== Test terminé ===');
  
  if (flash15.isEmpty || flash25.isEmpty) {
    print('ATTENTION: Certains modèles Flash sont manquants!');
    print('Utilisez le bouton "Actualiser" dans la page de configuration IA.');
  } else {
    print('SUCCESS: Tous les modèles Gemini Flash sont présents!');
  }
}
